// 简单的i18n测试
import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'

const zhCN = {
  "common": {
    "loading": "加载中...",
    "success": "成功"
  }
}

const enUS = {
  "common": {
    "loading": "Loading...",
    "success": "Success"
  }
}

i18n
  .use(initReactI18next)
  .init({
    resources: {
      zh: {
        translation: zhCN
      },
      en: {
        translation: enUS
      }
    },
    fallbackLng: 'zh',
    debug: false,
    interpolation: {
      escapeValue: false
    }
  })

export default i18n
