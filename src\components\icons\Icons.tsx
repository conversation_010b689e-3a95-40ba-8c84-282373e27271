import React from 'react';

// 通用图标属性接口
interface IconProps {
  width?: number | string;
  height?: number | string;
  className?: string;
  stroke?: string;
  strokeWidth?: number | string;
  fill?: string;
}

// 默认图标属性
const defaultProps: IconProps = {
  width: 16,
  height: 16,
  fill: 'none',
  stroke: 'currentColor',
  strokeWidth: 2,
};

// 下拉箭头图标
export const ChevronDownIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <polyline points="6 9 12 15 18 9"></polyline>
    </svg>
  );
};

// 检查标记图标
export const CheckIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <polyline points="20 6 9 17 4 12"></polyline>
    </svg>
  );
};

// 搜索图标
export const SearchIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <circle cx="11" cy="11" r="8"></circle>
      <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
    </svg>
  );
};

// 展开图标
export const ExpandIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <polyline points="15 3 21 3 21 9"></polyline>
      <polyline points="9 21 3 21 3 15"></polyline>
      <line x1="21" y1="3" x2="14" y2="10"></line>
      <line x1="3" y1="21" x2="10" y2="14"></line>
    </svg>
  );
};

// 来源图标
export const SourcesIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M4 7V4h3"></path>
      <path d="M20 7V4h-3"></path>
      <path d="M4 17v3h3"></path>
      <path d="M20 17v3h-3"></path>
      <path d="M12 4v16"></path>
      <path d="M8 12H4"></path>
      <path d="M14 12h-4"></path>
      <path d="M20 12h-4"></path>
    </svg>
  );
};

// 加号图标 - 用于新建会话
export const PlusIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <line x1="12" y1="5" x2="12" y2="19"></line>
      <line x1="5" y1="12" x2="19" y2="12"></line>
    </svg>
  );
};

// 返回箭头图标 - 用于返回上一页
export const ArrowLeftIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="m12 19-7-7 7-7"></path>
      <path d="M19 12H5"></path>
    </svg>
  );
};

// 首页图标 - 用于返回首页
export const HomeIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
      <polyline points="9,22 9,12 15,12 15,22"></polyline>
    </svg>
  );
};

// 现代化退出图标 - 箭头指向门的样式
export const LogoutIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
      <polyline points="16,17 21,12 16,7"></polyline>
      <line x1="21" y1="12" x2="9" y2="12"></line>
    </svg>
  );
};

// 电源按钮样式的退出图标
export const PowerOffIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M18.36 6.64a9 9 0 1 1-12.73 0"></path>
      <line x1="12" y1="2" x2="12" y2="12"></line>
    </svg>
  );
};

// 历史记录图标 - 用于查看历史对话
export const HistoryIcon: React.FC<IconProps> = (props) => {
  const { width, height, className, stroke, strokeWidth, fill } = { ...defaultProps, ...props };
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      aria-label="历史记录"
    >
      <circle cx="12" cy="12" r="10"></circle>
      <polyline points="12,6 12,12 16,14"></polyline>
    </svg>
  );
};

// 导出所有图标的对象，方便批量导入
export const Icons = {
  ChevronDown: ChevronDownIcon,
  Check: CheckIcon,
  Search: SearchIcon,
  Expand: ExpandIcon,
  Sources: SourcesIcon,
  Plus: PlusIcon,
  Home: HomeIcon,
  Logout: LogoutIcon,
  PowerOff: PowerOffIcon,
  History: HistoryIcon,
};

export default Icons;