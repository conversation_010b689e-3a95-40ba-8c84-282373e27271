import React from 'react';
import AssistantMessage from './AssistantMessage';

// 测试移动端布局的示例数据
const testData = {
  content: "这是一个测试回答内容",
  thinking: "这是思考过程的内容",
  isGenerating: false,
  searchResults: [
    { title: "百度百科", type: "docs", url: "https://baike.baidu.com" },
    { title: "下厨房", type: "web", url: "https://xiachufang.com" }
  ],
  browsingResults: ["浏览结果1", "浏览结果2"],
  workflowNodes: [
    {
      id: "node1",
      type: "question-classifier",
      title: "分析问题需求",
      status: "success" as const,
      created_at: Date.now() - 3000,
      displayCategory: "left" as const,
      outputs: {
        answer: "目前正在查看用户的请求，关于"精确里程制作"的问题。"
      }
    },
    {
      id: "node2", 
      type: "llm",
      title: "整合网络信息",
      status: "running" as const,
      created_at: Date.now() - 2000,
      displayCategory: "left" as const,
      outputs: {
        answer: "正在整合来自多个网络源的信息..."
      }
    },
    {
      id: "node3",
      type: "answer",
      title: "生成最终回答",
      status: "running" as const,
      created_at: Date.now() - 1000,
      displayCategory: "result" as const,
      outputs: {
        answer: "基于收集的信息，我来为您详细介绍精确里程制作的方法..."
      }
    }
  ],
  leftPanelNodes: [
    {
      id: "node1",
      type: "question-classifier", 
      title: "分析问题需求",
      status: "success" as const,
      created_at: Date.now() - 3000,
      displayCategory: "left" as const,
      outputs: {
        answer: "目前正在查看用户的请求，关于"精确里程制作"的问题。"
      }
    },
    {
      id: "node2",
      type: "llm",
      title: "整合网络信息", 
      status: "running" as const,
      created_at: Date.now() - 2000,
      displayCategory: "left" as const,
      outputs: {
        answer: "正在整合来自多个网络源的信息..."
      }
    }
  ],
  rightPanelNodes: [],
  resultNodes: [
    {
      id: "node3",
      type: "answer",
      title: "生成最终回答",
      status: "running" as const,
      created_at: Date.now() - 1000,
      displayCategory: "result" as const,
      outputs: {
        answer: "基于收集的信息，我来为您详细介绍精确里程制作的方法..."
      }
    }
  ],
  workflowStatus: "running" as const
};

const MobileLayoutTest: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-100">
      {/* 模拟移动端视口 */}
      <div className="max-w-sm mx-auto bg-white min-h-screen">
        <div className="p-4">
          <h1 className="text-lg font-bold mb-4 text-center text-gray-800">移动端布局测试</h1>
          <div className="space-y-4">
            <AssistantMessage {...testData} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileLayoutTest;
