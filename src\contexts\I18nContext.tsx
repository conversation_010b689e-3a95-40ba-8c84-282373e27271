import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import type { SupportedLanguage } from '../i18n'
import type { I18nContextType, LanguageChangeListener } from '../types/i18n'
import { getValidLanguage, saveLanguagePreference, getSavedLanguage } from '../i18n/utils'

// 创建国际化上下文
const I18nContext = createContext<I18nContextType | undefined>(undefined)

interface I18nProviderProps {
  children: React.ReactNode
  defaultLanguage?: SupportedLanguage
  onLanguageChange?: LanguageChangeListener
}

/**
 * 国际化上下文提供者
 * 管理全局的语言状态和翻译功能
 */
export const I18nProvider: React.FC<I18nProviderProps> = ({
  children,
  defaultLanguage = 'zh',
  onLanguageChange
}) => {
  const { t, i18n } = useTranslation()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 当前语言状态
  const currentLanguage = getValidLanguage(i18n.language) as SupportedLanguage

  /**
   * 切换语言
   */
  const changeLanguage = useCallback(
    async (lang: SupportedLanguage) => {
      if (lang === currentLanguage) return

      setIsLoading(true)
      setError(null)

      try {
        const oldLanguage = currentLanguage
        
        // 更新i18n语言
        await i18n.changeLanguage(lang)
        
        // 保存语言偏好
        saveLanguagePreference(lang)
        
        // 触发语言变更回调
        onLanguageChange?.(lang, oldLanguage)
        
        console.log(`Language changed from ${oldLanguage} to ${lang}`)
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to change language'
        setError(errorMessage)
        console.error('Failed to change language:', err)
      } finally {
        setIsLoading(false)
      }
    },
    [currentLanguage, i18n, onLanguageChange]
  )

  /**
   * 初始化语言设置
   */
  useEffect(() => {
    const initializeLanguage = async () => {
      try {
        // 获取保存的语言偏好
        const savedLanguage = getSavedLanguage()
        
        // 如果保存的语言与当前语言不同，切换到保存的语言
        if (savedLanguage !== currentLanguage) {
          await changeLanguage(savedLanguage)
        }
      } catch (err) {
        console.warn('Failed to initialize language:', err)
        // 如果初始化失败，使用默认语言
        if (currentLanguage !== defaultLanguage) {
          await changeLanguage(defaultLanguage)
        }
      }
    }

    initializeLanguage()
  }, []) // 只在组件挂载时执行一次

  /**
   * 监听i18n语言变化
   */
  useEffect(() => {
    const handleLanguageChanged = (lng: string) => {
      const newLang = getValidLanguage(lng) as SupportedLanguage
      if (newLang !== currentLanguage) {
        // 同步保存语言偏好
        saveLanguagePreference(newLang)
      }
    }

    i18n.on('languageChanged', handleLanguageChanged)
    
    return () => {
      i18n.off('languageChanged', handleLanguageChanged)
    }
  }, [i18n, currentLanguage])

  /**
   * 增强的翻译函数
   */
  const enhancedT = useCallback(
    (key: string, options?: any): string => {
      try {
        const result = t(key, options)
        return typeof result === 'string' ? result : key
      } catch (err) {
        console.warn(`Translation failed for key: ${key}`, err)
        return key
      }
    },
    [t]
  )

  const contextValue: I18nContextType = {
    currentLanguage,
    changeLanguage,
    t: enhancedT,
    isLoading,
    error
  }

  return (
    <I18nContext.Provider value={contextValue}>
      {children}
    </I18nContext.Provider>
  )
}

/**
 * 使用国际化上下文的Hook
 */
export const useI18nContext = (): I18nContextType => {
  const context = useContext(I18nContext)
  
  if (context === undefined) {
    throw new Error('useI18nContext must be used within an I18nProvider')
  }
  
  return context
}

/**
 * 语言状态Hook
 */
export const useLanguageState = () => {
  const { currentLanguage, changeLanguage, isLoading, error } = useI18nContext()
  
  return {
    language: currentLanguage,
    setLanguage: changeLanguage,
    isChanging: isLoading,
    error
  }
}

/**
 * 翻译Hook
 */
export const useTranslate = () => {
  const { t } = useI18nContext()
  return t
}

/**
 * 语言切换Hook
 */
export const useLanguageSwitch = () => {
  const { currentLanguage, changeLanguage, isLoading } = useI18nContext()
  
  const switchToNext = useCallback(() => {
    const nextLang = currentLanguage === 'zh' ? 'en' : 'zh'
    changeLanguage(nextLang)
  }, [currentLanguage, changeLanguage])
  
  const switchTo = useCallback(
    (lang: SupportedLanguage) => {
      changeLanguage(lang)
    },
    [changeLanguage]
  )
  
  return {
    currentLanguage,
    switchToNext,
    switchTo,
    isLoading
  }
}

export default I18nContext
