import React from 'react'
import { useSimpleTranslation, useI18nRouter } from '../i18n/simple-hooks'
import LanguageSwitcher from './LanguageSwitcher'
import { formatDate, formatNumber, formatCurrency, formatFileSize } from '../i18n/helpers'

/**
 * 国际化测试页面
 * 用于验证国际化功能是否正常工作
 */
const I18nTestPage: React.FC = () => {
  const { t, currentLanguage } = useSimpleTranslation()
  const { currentAppName, currentSessionId, navigateToHome, navigateToApp } = useI18nRouter()

  const testData = {
    number: 1234567.89,
    currency: 9999.99,
    date: new Date(),
    fileSize: 1024 * 1024 * 2.5 // 2.5MB
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            {t('common.loading')} - 国际化测试页面
          </h1>

          {/* 语言切换器测试 */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">语言切换器测试</h2>
            <div className="flex gap-4 items-center">
              <span>当前语言: {currentLanguage}</span>
              <LanguageSwitcher />
              <LanguageSwitcher showText={false} size="small" />
              <LanguageSwitcher showFlag={false} size="large" />
            </div>
          </div>

          {/* 翻译测试 */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">翻译功能测试</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h3 className="font-medium">通用翻译</h3>
                <ul className="space-y-1 text-sm">
                  <li>加载中: {t('common.loading')}</li>
                  <li>成功: {t('common.success')}</li>
                  <li>错误: {t('common.error')}</li>
                  <li>确认: {t('common.confirm')}</li>
                  <li>取消: {t('common.cancel')}</li>
                </ul>
              </div>
              
              <div className="space-y-2">
                <h3 className="font-medium">头部翻译</h3>
                <ul className="space-y-1 text-sm">
                  <li>标题: {t('header.title')}</li>
                  <li>菜单: {t('header.menu')}</li>
                  <li>用户: {t('header.user')}</li>
                  <li>语言: {t('header.language')}</li>
                  <li>切换语言: {t('header.switchLanguage')}</li>
                </ul>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">聊天翻译</h3>
                <ul className="space-y-1 text-sm">
                  <li>新建对话: {t('chat.newChat')}</li>
                  <li>发送: {t('chat.send')}</li>
                  <li>停止: {t('chat.stop')}</li>
                  <li>思考中: {t('chat.thinking')}</li>
                  <li>生成中: {t('chat.generating')}</li>
                </ul>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">认证翻译</h3>
                <ul className="space-y-1 text-sm">
                  <li>登录: {t('auth.loginButton')}</li>
                  <li>退出登录: {t('auth.logoutButton')}</li>
                  <li>已登录: {t('auth.loggedIn')}</li>
                  <li>请先登录: {t('auth.loginRequired')}</li>
                </ul>
              </div>
            </div>
          </div>

          {/* 格式化测试 */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">格式化功能测试</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h3 className="font-medium">数字格式化</h3>
                <ul className="space-y-1 text-sm">
                  <li>原始数字: {testData.number}</li>
                  <li>格式化: {formatNumber(testData.number, currentLanguage)}</li>
                  <li>货币: {formatCurrency(testData.currency, currentLanguage)}</li>
                  <li>文件大小: {formatFileSize(testData.fileSize, currentLanguage)}</li>
                </ul>
              </div>
              
              <div className="space-y-2">
                <h3 className="font-medium">日期格式化</h3>
                <ul className="space-y-1 text-sm">
                  <li>完整日期: {formatDate(testData.date, currentLanguage)}</li>
                  <li>短日期: {formatDate(testData.date, currentLanguage, { 
                    year: 'numeric', 
                    month: 'short', 
                    day: 'numeric' 
                  })}</li>
                  <li>时间: {formatDate(testData.date, currentLanguage, { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}</li>
                </ul>
              </div>
            </div>
          </div>

          {/* 路由测试 */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">路由功能测试</h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">当前路由信息</h3>
                <ul className="space-y-1 text-sm">
                  <li>当前语言: {currentLanguage}</li>
                  <li>应用名称: {currentAppName}</li>
                  <li>会话ID: {currentSessionId || '无'}</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-medium mb-2">导航测试</h3>
                <div className="flex gap-2">
                  <button
                    onClick={() => navigateToHome()}
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    回到首页
                  </button>
                  <button
                    onClick={() => navigateToApp('novax-base', 'test-session-123')}
                    className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                  >
                    测试聊天页面
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* 工作流节点翻译测试 */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">工作流节点翻译测试</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
              <div>开始节点: {t('workflow.nodes.start')}</div>
              <div>条件判断: {t('workflow.nodes.ifElse')}</div>
              <div>文档提取: {t('workflow.nodes.documentExtractor')}</div>
              <div>工具调用: {t('workflow.nodes.tool')}</div>
              <div>AI分析: {t('workflow.nodes.llm')}</div>
              <div>生成回答: {t('workflow.nodes.answer')}</div>
            </div>
          </div>

          {/* 错误消息测试 */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">错误消息测试</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <div className="text-red-600">网络错误: {t('errors.networkError')}</div>
                <div className="text-red-600">服务器错误: {t('errors.serverError')}</div>
                <div className="text-red-600">未授权: {t('errors.unauthorized')}</div>
              </div>
              <div>
                <div className="text-green-600">保存成功: {t('messages.saveSuccess')}</div>
                <div className="text-green-600">操作成功: {t('messages.operationSuccess')}</div>
                <div className="text-blue-600">处理中: {t('messages.processingRequest')}</div>
              </div>
            </div>
          </div>

          {/* 应用信息测试 */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">应用信息测试</h2>
            <div className="space-y-2">
              <div>NovaX Base: {t('apps.novaxBase.name')} - {t('apps.novaxBase.description')}</div>
              <div>NovaX Pro: {t('apps.novaxPro.name')} - {t('apps.novaxPro.description')}</div>
              <div>NovaX Ultra: {t('apps.novaxUltra.name')} - {t('apps.novaxUltra.description')}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default I18nTestPage
