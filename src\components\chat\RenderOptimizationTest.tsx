import React, { useState, useEffect } from 'react';
import AssistantMessage from './AssistantMessage';

// 模拟工作流节点数据
const createMockNode = (id: string, status: 'running' | 'success' | 'error' = 'running') => ({
  id,
  type: 'llm',
  title: `节点 ${id}`,
  status,
  created_at: Date.now(),
  displayCategory: 'right' as const,
  outputs: { answer: `这是节点 ${id} 的输出内容` }
});

// 渲染优化测试组件
const RenderOptimizationTest: React.FC = () => {
  const [nodes, setNodes] = useState([createMockNode('1')]);
  const [renderCount, setRenderCount] = useState(0);

  // 模拟流式接口不断新增节点
  useEffect(() => {
    const interval = setInterval(() => {
      setNodes(prev => {
        const newNodeId = (prev.length + 1).toString();
        const newNode = createMockNode(newNodeId);
        
        // 模拟之前节点完成，新节点开始
        const updatedNodes = prev.map(node => ({
          ...node,
          status: 'success' as const
        }));
        
        return [...updatedNodes, newNode];
      });
    }, 1000);

    // 10秒后停止
    const timeout = setTimeout(() => {
      clearInterval(interval);
    }, 10000);

    return () => {
      clearInterval(interval);
      clearTimeout(timeout);
    };
  }, []);

  // 监控渲染次数
  useEffect(() => {
    setRenderCount(prev => prev + 1);
  });

  const mockProps = {
    content: '测试内容',
    thinking: '测试思考过程',
    isGenerating: nodes.some(node => node.status === 'running'),
    workflowNodes: nodes,
    leftPanelNodes: [],
    rightPanelNodes: nodes,
    resultNodes: [],
    searchResults: [],
    browsingResults: []
  };

  return (
    <div className="p-4">
      <div className="mb-4 p-4 bg-blue-50 rounded-lg">
        <h2 className="text-lg font-bold mb-2">渲染优化测试</h2>
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div>
            <strong>节点数量:</strong> {nodes.length}
          </div>
          <div>
            <strong>渲染次数:</strong> {renderCount}
          </div>
          <div>
            <strong>运行状态:</strong> {nodes.some(node => node.status === 'running') ? '进行中' : '已完成'}
          </div>
        </div>
        <div className="mt-2 text-xs text-gray-600">
          每秒新增一个节点，观察渲染次数变化。优化后应该显著减少渲染次数。
        </div>
      </div>
      
      <AssistantMessage {...mockProps} />
    </div>
  );
};

export default RenderOptimizationTest;
