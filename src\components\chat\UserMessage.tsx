import React from 'react'

// 文件上传项类型定义
interface UploadFileItem {
  uid: string
  name: string
  status: 'uploading' | 'done' | 'error'
  size: number
  type: 'document' | 'image' | 'audio' | 'video'
  originFileObj?: { uid: string }
  percent: number
  transfer_method: 'local_file'
  upload_file_id?: string
  error?: string
}

interface UserMessageProps {
  content: string
  files?: UploadFileItem[]
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 获取文件类型图标
const getFileIcon = (type: string): string => {
  switch (type) {
    case 'image':
      return '🖼️';
    case 'document':
      return '📄';
    case 'audio':
      return '🎵';
    case 'video':
      return '🎬';
    default:
      return '📎';
  }
};

/**
 * 用户消息组件 - 显示用户发送的消息
 * 采用右对齐布局，灰色背景样式
 */
const UserMessage: React.FC<UserMessageProps> = ({ content, files }) => {
  return (
    <div className="mx-auto max-w-4xl relative group">
      <div className="ml-auto max-w-2xl bg-gray-0 border border-gray-200 rounded-2xl rounded-br-md p-4 shadow-sm hover:shadow-md transition-all duration-200">
        {/* 文字内容 */}
        {content && (
          <div className="text-gray-800 text-base leading-relaxed whitespace-pre-wrap">
            {content}
          </div>
        )}

        {/* 附件列表 */}
        {files && files.length > 0 && (
          <div className="space-y-2 mt-3">
            {files.map((file) => (
              <div
                key={file.uid}
                className="flex items-center space-x-3 p-3 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
              >
                {/* 文件图标 */}
                <div className="flex-shrink-0 text-lg">
                  {getFileIcon(file.type)}
                </div>

                {/* 文件信息 */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(file.size)}
                    {file.status === 'uploading' && ` • 上传中 ${file.percent}%`}
                    {file.status === 'error' && file.error && ` • ${file.error}`}
                    {file.status === 'done' && ` • 已上传`}
                  </p>
                </div>

                {/* 状态指示器 */}
                <div className="flex-shrink-0">
                  {file.status === 'uploading' && (
                    <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                  )}
                  {file.status === 'done' && (
                    <div className="text-green-500">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  )}
                  {file.status === 'error' && (
                    <div className="text-red-500">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default UserMessage