import React, { useState } from 'react'
import ChatInput from './chat/ChatInput'

const StopButtonTestPage: React.FC = () => {
  const [inputValue, setInputValue] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)

  const handleSubmit = () => {
    if (!inputValue.trim()) return
    
    console.log('发送消息:', inputValue)
    setIsGenerating(true)
    
    // 模拟生成过程，5秒后自动停止
    setTimeout(() => {
      setIsGenerating(false)
    }, 5000)
  }

  const handleStop = () => {
    console.log('停止生成')
    setIsGenerating(false)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault()
      if (!isGenerating) {
        handleSubmit()
      }
    }
  }

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            停止按钮功能测试
          </h1>
          <p className="text-gray-600 mb-6">
            测试发送按钮在生成状态时变为停止按钮的功能
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">功能说明</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2">发送状态</h3>
              <ul className="text-blue-700 space-y-1">
                <li>• 紫色渐变背景</li>
                <li>• 飞机图标</li>
                <li>• 点击发送消息</li>
                <li>• 悬停时有起飞动画</li>
              </ul>
            </div>
            <div className="bg-red-50 p-4 rounded-lg">
              <h3 className="font-medium text-red-900 mb-2">停止状态</h3>
              <ul className="text-red-700 space-y-1">
                <li>• 红色渐变背景</li>
                <li>• 方形停止图标</li>
                <li>• 点击停止生成</li>
                <li>• 红色阴影效果</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">测试步骤</h2>
          <ol className="list-decimal list-inside space-y-2 text-gray-700">
            <li>在下方输入框中输入一些文字</li>
            <li>点击发送按钮（紫色飞机图标）</li>
            <li>观察按钮变为红色停止图标</li>
            <li>点击停止按钮测试停止功能</li>
            <li>或等待5秒自动停止</li>
          </ol>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6 mb-32">
          <h2 className="text-xl font-semibold mb-4">当前状态</h2>
          <div className="flex items-center gap-4 mb-4">
            <span className="text-gray-600">生成状态:</span>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              isGenerating 
                ? 'bg-red-100 text-red-800' 
                : 'bg-green-100 text-green-800'
            }`}>
              {isGenerating ? '正在生成...' : '空闲'}
            </span>
          </div>
          <div className="flex items-center gap-4">
            <span className="text-gray-600">输入内容:</span>
            <span className="text-gray-800 font-mono bg-gray-100 px-2 py-1 rounded">
              {inputValue || '(空)'}
            </span>
          </div>
        </div>
      </div>

      {/* ChatInput 组件 */}
      <ChatInput
        inputValue={inputValue}
        isGenerating={isGenerating}
        onInputChange={setInputValue}
        onSubmit={handleSubmit}
        onStop={handleStop}
        onKeyDown={handleKeyDown}
      />
    </div>
  )
}

export default StopButtonTestPage
