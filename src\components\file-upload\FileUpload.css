/* FileUpload 组件的拖拽动画效果 */

.fade-in {
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.drag-over-effect {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: scale(1.02);
}

.drag-over-effect::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
  border-radius: inherit;
  z-index: -1;
  opacity: 0.1;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.1;
  }
  50% {
    opacity: 0.2;
  }
}

/* 拖拽区域高亮效果 */
.drag-highlight {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(29, 78, 216, 0.05) 100%);
  border: 2px dashed rgba(59, 130, 246, 0.3);
  border-radius: 16px;
}

/* 文件图标动画 */
.file-icon-bounce {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* 拖拽覆盖层渐变背景 */
.drag-overlay {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.1) 0%, 
    rgba(29, 78, 216, 0.15) 50%, 
    rgba(59, 130, 246, 0.1) 100%);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* 响应式设计 */
@media (max-width: 640px) {
  .drag-overlay .text-4xl {
    font-size: 2rem;
  }
  
  .drag-overlay .text-lg {
    font-size: 1rem;
  }
}
