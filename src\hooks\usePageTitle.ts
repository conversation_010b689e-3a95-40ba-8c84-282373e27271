import { useEffect, useRef } from 'react'
import { useLocation } from 'react-router-dom'
import { useSimpleTranslation } from '../i18n/simple-hooks'

export interface UsePageTitleOptions {
  /** 页面类型 */
  pageType?: 'home' | 'chat' | 'newChat' | 'loading' | 'error' | 'notFound'
  /** 应用名称键 */
  appKey?: string
  /** 自定义标题（优先级最高） */
  customTitle?: string
  /** 对话标题（用于对话页面） */
  chatTitle?: string
  /** 是否显示应用名称 */
  showAppName?: boolean
  /** 是否显示后缀 */
  showSuffix?: boolean
  /** 自定义分隔符 */
  separator?: string
  /** 最大标题长度（超出会截断） */
  maxLength?: number
  /** 是否启用自动路由检测 */
  autoDetect?: boolean
}

/**
 * 页面标题管理Hook
 * 提供便捷的API来设置和管理页面标题
 * 支持自动路由检测、国际化、动态更新
 */
export const usePageTitle = (options: UsePageTitleOptions = {}) => {
  const { t } = useSimpleTranslation()
  const location = useLocation()
  const titleRef = useRef<string>('')

  const {
    pageType = 'home',
    appKey,
    customTitle,
    chatTitle,
    showAppName = true,
    showSuffix = true,
    separator,
    maxLength = 60,
    autoDetect = true
  } = options

  // 从路由自动检测应用和页面类型
  const detectFromRoute = () => {
    const segments = location.pathname.split('/').filter(Boolean)
    
    // 路由格式: /{language}/{app-name}/[conversationId]
    let detectedAppKey = appKey
    let detectedPageType = pageType
    
    if (segments.length >= 2) {
      const appName = segments[1]
      if (appName && ['novax-base', 'novax-pro', 'novax-ultra', 'elavax-base', 'elavax-pro', 'elavax-ultra'].includes(appName)) {
        detectedAppKey = appName
      }
    }
    
    if (segments.length >= 3) {
      const conversationId = segments[2]
      if (conversationId === 'new' || conversationId === '') {
        detectedPageType = 'newChat'
      } else {
        detectedPageType = 'chat'
      }
    } else if (segments.length === 2) {
      detectedPageType = 'home'
    }
    
    return { detectedAppKey, detectedPageType }
  }

  // 生成标题
  const generateTitle = () => {
    // 如果有自定义标题，直接使用
    if (customTitle) {
      return truncateTitle(customTitle, maxLength)
    }

    // 自动检测路由信息
    const { detectedAppKey, detectedPageType } = autoDetect ? detectFromRoute() : { detectedAppKey: appKey, detectedPageType: pageType }

    // 获取翻译值
    const pageTitleSeparator = separator || t('pageTitle.separator')
    const suffix = t('pageTitle.suffix')
    
    // 获取应用名称
    const appName = detectedAppKey ? t(`pageTitle.apps.${detectedAppKey}`) : ''
    
    // 根据页面类型生成标题
    let titleParts: string[] = []
    
    switch (detectedPageType) {
      case 'home':
        if (showAppName && appName) {
          titleParts.push(appName)
        }
        break
        
      case 'chat':
        if (chatTitle) {
          titleParts.push(chatTitle)
        } else {
          titleParts.push(t('pageTitle.chat'))
        }
        if (showAppName && appName) {
          titleParts.push(appName)
        }
        break
        
      case 'newChat':
        titleParts.push(t('pageTitle.newChat'))
        if (showAppName && appName) {
          titleParts.push(appName)
        }
        break
        
      case 'loading':
        titleParts.push(t('pageTitle.loading'))
        if (showAppName && appName) {
          titleParts.push(appName)
        }
        break
        
      case 'error':
        titleParts.push(t('pageTitle.error'))
        if (showAppName && appName) {
          titleParts.push(appName)
        }
        break
        
      case 'notFound':
        titleParts.push(t('pageTitle.notFound'))
        break
        
      default:
        if (showAppName && appName) {
          titleParts.push(appName)
        }
    }
    
    // 添加后缀
    if (showSuffix) {
      titleParts.push(suffix)
    }
    
    // 组合标题
    const finalTitle = titleParts.join(pageTitleSeparator)
    
    return truncateTitle(finalTitle, maxLength)
  }

  // 截断标题函数
  const truncateTitle = (title: string, maxLen: number): string => {
    if (title.length <= maxLen) {
      return title
    }
    
    // 智能截断：尽量保留完整的词语
    const truncated = title.substring(0, maxLen - 3)
    const lastSpace = truncated.lastIndexOf(' ')
    const lastSeparator = truncated.lastIndexOf(' - ')
    
    // 如果找到分隔符，在分隔符处截断
    if (lastSeparator > 0 && lastSeparator > truncated.length * 0.6) {
      return truncated.substring(0, lastSeparator) + '...'
    }
    
    // 如果找到空格，在空格处截断
    if (lastSpace > 0 && lastSpace > truncated.length * 0.7) {
      return truncated.substring(0, lastSpace) + '...'
    }
    
    // 否则直接截断
    return truncated + '...'
  }

  // 设置页面标题
  const setTitle = (newTitle: string) => {
    const finalTitle = truncateTitle(newTitle, maxLength)
    document.title = finalTitle
    titleRef.current = finalTitle

    if (import.meta.env.DEV) {
      console.log('[usePageTitle] Title updated:', finalTitle)
    }
  }

  // 更新标题
  useEffect(() => {
    const newTitle = generateTitle()
    if (newTitle !== titleRef.current) {
      setTitle(newTitle)
    }
  }, [
    customTitle,
    pageType,
    appKey,
    chatTitle,
    showAppName,
    showSuffix,
    separator,
    maxLength,
    autoDetect,
    location.pathname,
    t
  ])

  return {
    /** 当前标题 */
    title: titleRef.current,
    /** 手动设置标题 */
    setTitle,
    /** 生成标题（不设置） */
    generateTitle
  }
}

export default usePageTitle
