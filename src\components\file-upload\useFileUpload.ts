import { useState, useCallback } from 'react'
import { message } from 'antd'
import { Dify<PERSON>pi, IFileLocal } from '../../api/src/dify-api'
import { IFileType } from '../../api/src/types'
import { getFileTypeByName } from '../../api/src/utils/file-type'
import { useSimpleTranslation } from '../../i18n/simple-hooks'

export interface UseFileUploadOptions {
  difyApi: DifyApi
  maxFiles?: number
  onPreCheck?: () => boolean
  onUploadSuccess?: (files: File[], uploadData: IFileLocal[]) => void
  onUploadError?: (error: string) => void
}

export interface UseFileUploadReturn {
  uploadedFiles: File[]
  uploadData: IFileLocal[]
  isUploading: boolean
  handleFilesUploaded: (files: File[]) => Promise<void>
  removeFile: (index: number) => void
  clearFiles: () => void
}

export const useFileUpload = ({
  difyApi,
  maxFiles = 10,
  onPreCheck,
  onUploadSuccess,
  onUploadError
}: UseFileUploadOptions): UseFileUploadReturn => {
  const { t } = useSimpleTranslation()
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])
  const [uploadData, setUploadData] = useState<IFileLocal[]>([])
  const [isUploading, setIsUploading] = useState(false)

  const handleFilesUploaded = useCallback(async (files: File[]) => {
    // 预检查（登录、支付状态等）
    if (onPreCheck && !onPreCheck()) {
      return
    }

    setIsUploading(true)

    try {
      // 限制文件数量
      const limitFiles = files.slice(0, maxFiles)
      if (limitFiles.length < files.length) {
        message.warning(t('chat.fileUploadWarning').replace('{maxFiles}', maxFiles.toString()).replace('{limitFiles}', maxFiles.toString()))
      }

      const successFiles: File[] = []
      const successUploads: IFileLocal[] = []

      // 逐个处理文件上传
      for (const file of limitFiles) {
        try {
          const res: any = await difyApi.uploadFile(file)

          if (res.code !== 0) {
            message.error(`${t('chat.uploadFailed')}: ${file.name} - ${res.msg}`)
            continue // 继续处理下一个文件，而不是直接返回
          }

          successFiles.push(file)
          successUploads.push({
            name: file.name,
            size: file.size,
            transfer_method: 'local_file',
            upload_file_id: res.data.id,
            type: getFileTypeByName(file.name) as unknown as IFileType,
          })

          console.log(t('chat.uploadSuccess').replace('{fileName}', file.name).replace('{fileId}', res.data.id))
        } catch (error) {
          console.error(`${t('chat.uploadFailed')}: ${file.name}`, error)
          message.error(`${t('chat.uploadFailed')}: ${file.name}`)
        }
      }

      // 更新状态 - 只有成功上传的文件
      if (successFiles.length > 0) {
        message.success(`${t('fileUpload.uploadSuccess')} ${successFiles.length} ${t('common.file')}`)

        // 使用FIFO队列行为更新文件列表
        setUploadedFiles(prevFiles => {
          const updatedFiles = [...prevFiles, ...successFiles]
          while (updatedFiles.length > maxFiles) {
            updatedFiles.shift()
          }
          return updatedFiles
        })

        setUploadData(prevData => {
          const updatedData = [...prevData, ...successUploads]
          while (updatedData.length > maxFiles) {
            updatedData.shift()
          }
          return updatedData
        })

        // 调用成功回调
        onUploadSuccess?.(successFiles, successUploads)
      }
    } catch (error) {
      console.error('文件上传过程发生错误:', error)
      const errorMessage = '文件上传过程发生错误，请重试'
      message.error(errorMessage)
      onUploadError?.(errorMessage)
    } finally {
      setIsUploading(false)
    }
  }, [difyApi, maxFiles, onPreCheck, onUploadSuccess, onUploadError])

  const removeFile = useCallback((index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index))
    setUploadData(prev => prev.filter((_, i) => i !== index))
  }, [])

  const clearFiles = useCallback(() => {
    setUploadedFiles([])
    setUploadData([])
  }, [])

  return {
    uploadedFiles,
    uploadData,
    isUploading,
    handleFilesUploaded,
    removeFile,
    clearFiles
  }
}
