export function generateUUID() {
	return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
	  const r = Math.random() * 16 | 0;
	  const v = c === 'x' ? r : (r & 0x3 | 0x8);
	  return v.toString(16);
	});
  }

  // 设备检测工具函数
export function isMobile() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;
};

export function formatPeriodText(monthNum: number, periodType: string, t?: (key: string) => string) {
    // 如果没有传入翻译函数，使用默认中文（保持向后兼容）
    if (!t) {
      if (periodType === 'DAY') {
        return monthNum === 7 ? '/周' : `/${monthNum}天`;
      } else if (periodType === 'MONTH') {
        if (monthNum === 1) return '/月';
        if (monthNum === 3) return '/季';
        if (monthNum === 12) return '/年';
        return `/${monthNum}月`;
      }
      return '/月';
    }

    // 使用国际化翻译
    if (periodType === 'DAY') {
      return monthNum === 7 ? t('subscription.perWeek') : `/${monthNum}${t('subscription.perDay')}`;
    } else if (periodType === 'MONTH') {
      if (monthNum === 1) return t('subscription.perMonth');
      if (monthNum === 3) return t('subscription.perQuarter');
      if (monthNum === 12) return t('subscription.perYear');
      return `/${monthNum}${t('subscription.perMonthUnit')}`;
    }
    return t('subscription.perMonth');
}