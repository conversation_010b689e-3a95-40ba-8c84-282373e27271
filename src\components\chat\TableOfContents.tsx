import React, { useMemo } from 'react'

interface TocItem {
  id: string
  title: string
  level: number
  element?: HTMLElement
}

interface TableOfContentsProps {
  content: string
  messageId: string
}

// Helper function to determine if an item has children
const hasChildren = (item: TocItem, currentIndex: number, allItems: TocItem[]): boolean => {
  if (currentIndex + 1 >= allItems.length) {
    return false; // Current item is the last in the list, so it cannot have children.
  }
  // A child exists if the next item in the list has a greater level.
  return allItems[currentIndex + 1].level > item.level;
};

/**
 * 目录组件 - 从消息内容中提取标题生成目录
 * 支持点击跳转到对应标题位置
 */
const TableOfContents: React.FC<TableOfContentsProps> = ({ content, messageId }) => {
  // 从内容中提取标题
  const tocItems = useMemo(() => {
    const items: TocItem[] = []
    
    // 匹配Markdown标题格式 (# ## ### 等)
    const headingRegex = /^(#{1,6})\s+(.+)$/gm
    let match
    
    while ((match = headingRegex.exec(content)) !== null) {
      const level = match[1].length // # 的数量决定标题级别
      const title = match[2].trim()
      
      items.push({
        id: `${messageId}-heading-${items.length}`,
        title,
        level
      })
    }
    
    // 如果没有找到Markdown标题，尝试匹配其他格式的标题
    if (items.length === 0) {
      // 匹配以数字开头的标题 (1. 2. 3. 等)
      const numberedRegex = /^(\d+\.\s+)(.+)$/gm
      while ((match = numberedRegex.exec(content)) !== null) {
        const title = match[2].trim()
        items.push({
          id: `${messageId}-numbered-${items.length}`,
          title,
          level: 1 // 默认数字列表为1级
        })
      }
      
      // 匹配以**开头的粗体标题 (作为备选，层级较低)
      const boldRegex = /\*\*([^*]+)\*\*/g
      while ((match = boldRegex.exec(content)) !== null) {
        const title = match[1].trim()
        // 过滤掉太短的粗体文本，避免误识别
        if (title.length > 3 && title.length < 50) {
          items.push({
            id: `${messageId}-bold-${items.length}`,
            title,
            level: 2 // 粗体标题默认为2级
          })
        }
      }
    }
    console.log('tocItems:', items);
    return items
  }, [content, messageId])

  // 处理目录项点击
  const handleTocClick = (item: TocItem) => {
    const headingId = `heading-${item.title.replace(/\s+/g, '-').toLowerCase()}`
    const element = document.getElementById(headingId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' })
    } else {
      const messageElement = document.querySelector(`[data-message-id="${messageId}"]`)
      if (messageElement) {
        const headings = messageElement.querySelectorAll('h1, h2, h3, h4, h5, h6')
        for (const heading of headings) {
          if (heading.textContent?.trim() === item.title.trim()) {
            heading.scrollIntoView({ behavior: 'smooth', block: 'start' })
            break
          }
        }
      }
    }
  }

  if (tocItems.length === 0) {
    return null
  }

  return (
    <div className="fixed right-8 top-16 w-56 bg-white/70 backdrop-blur-md border border-gray-200/70 rounded-lg shadow-lg p-3 overflow-y-auto max-h-[calc(100vh-100px)] z-50">
      {/* MODIFIED: 容器宽度和内边距调整, 圆角和阴影调整 */}
      <div className="sticky top-0 bg-white/80 backdrop-blur-sm -mx-3 -mt-3 px-3 pt-3 pb-2 mb-2 rounded-t-lg border-b border-gray-200/60">
        {/* MODIFIED: 内外边距和圆角匹配父容器, 边框颜色调整 */}
        <h3 className="text-sm font-medium text-gray-700 flex items-center gap-1.5">
          {/* MODIFIED: 标题文本样式调整 */}
           <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 10h16M4 14h16M4 18h7"></path></svg>
           {/* MODIFIED: 图标颜色和大小调整 */}
          <span>目录</span>
        </h3>
      </div>
      
      <nav className="space-y-0.5">
        {tocItems.map((item, index) => {
          const levelPaddings = { // 这个本身不直接用作class, 因为paddingLeft是动态计算的
            1: 'pl-0',
            2: 'pl-0',
            3: 'pl-0',
            4: 'pl-0',
            5: 'pl-0',
            6: 'pl-0'
          };

          // MODIFIED: 调整了字体大小和颜色，使其更统一和柔和
          const levelClasses = {
            1: 'font-medium text-gray-700 text-sm',
            2: 'text-gray-700 text-sm',
            3: 'text-gray-600 text-sm',
            4: 'text-gray-500 text-xs',
            5: 'text-gray-500 text-xs',
            6: 'text-gray-500 text-xs'
          };

          const textClass = levelClasses[item.level as keyof typeof levelClasses] || levelClasses[6];

          // MODIFIED: 调整了线条相关的几何参数，使布局更疏朗
          const baseIndentPx = 10;         // L1的水平线起点及后续垂直线的基准
          const indentPerLevelPx = 20;     // 每层增加的缩进量
          const lineBranchLengthPx = 14;   // 水平连接线分支的长度
          const textLeftMarginPx = 8;      // 文本相对于水平连接线末端的左边距

          const currentItemVerticalLineX = baseIndentPx + (item.level - 1) * indentPerLevelPx;
          const parentItemVerticalLineX = baseIndentPx + (item.level - 2) * indentPerLevelPx;
          const currentItemTextActualPaddingLeftPx = currentItemVerticalLineX + lineBranchLengthPx + textLeftMarginPx;

          let isLastChildOfItsParent = true; 
          if (item.level > 0) {
            for (let i = index + 1; i < tocItems.length; i++) {
              if (tocItems[i].level === item.level) {
                isLastChildOfItsParent = false;
                break;
              }
              if (tocItems[i].level < item.level) {
                break;
              }
            }
          }

          const currentItemHasChildren = hasChildren(item, index, tocItems);

          // 新增：判断当前一级目录项是否是所有一级目录项中的最后一个
          let isLastRootItem = false;
          if (item.level === 1) {
            isLastRootItem = true; // 先假设是最后一个
            for (let i = index + 1; i < tocItems.length; i++) {
              if (tocItems[i].level === 1) {
                isLastRootItem = false; // 如果后面还有一级目录项，则不是最后一个
                break;
              }
            }
          }

          return (
            <div key={`${item.id}-${index}`} className="relative group py-1">
              {/* --- 绘制连接线 --- */}
              {item.level === 1 && index > 0 && (
                <div 
                  className="absolute top-0 border-l border-dotted border-gray-300 group-hover:border-blue-400 transition-colors duration-150"
                  style={{
                    left: `${baseIndentPx}px`,
                    // 连接到当前项的中点
                    height: '50%', 
                  }}
                />
              )}
              {item.level === 1 && !isLastRootItem && (
                <div 
                  className="absolute border-l border-dotted border-gray-300 group-hover:border-blue-400 transition-colors duration-150"
                  style={{
                    left: `${baseIndentPx}px`,
                    top: '50%',
                    // 从当前项的中点连接到底部
                    height: '50%', 
                  }}
                />
              )}
              {item.level > 1 && (
                <div 
                  className="absolute top-0 border-l border-dotted border-gray-300 group-hover:border-blue-400 transition-colors duration-150"
                  style={{
                    left: `${parentItemVerticalLineX}px`,
                    height: isLastChildOfItsParent && !currentItemHasChildren ? '50%' : '100%', 
                  }}
                />
              )}

              <div 
                className="absolute border-t border-dotted border-gray-300 group-hover:border-blue-400 transition-colors duration-150"
                style={{
                  left: `${item.level === 1 ? baseIndentPx : parentItemVerticalLineX}px`, 
                  width: `${item.level === 1 ? lineBranchLengthPx : currentItemVerticalLineX - parentItemVerticalLineX + lineBranchLengthPx}px`,
                  top: '50%',
                  transform: 'translateY(-0.5px)',
                }}
              />

              {currentItemHasChildren && (
                 <div 
                  className="absolute border-l border-dotted border-gray-300 group-hover:border-blue-400 transition-colors duration-150"
                  style={{
                    left: `${currentItemVerticalLineX}px`,
                    top: '50%',
                    height: '50%',
                  }}
                />
              )}
              
              {(() => {
                const lines = [];
                for (let l = 1; l < item.level; l++) {
                  // 确保所有父级层级的垂直线都正确绘制，除非它是其兄弟节点中的最后一个
                  const ancestorVerticalLineX = baseIndentPx + (l - 1) * indentPerLevelPx;
                  let ancestorActualIndex = -1;
                  for(let k = index; k >= 0; k--){
                    if(tocItems[k].level === l){
                      ancestorActualIndex = k;
                      break;
                    }
                  }

                  if(ancestorActualIndex !== -1){
                    let ancestorIsLastAmongItsSiblings = true;
                    for (let i = ancestorActualIndex + 1; i < tocItems.length; i++) {
                        if (tocItems[i].level === l) {
                            ancestorIsLastAmongItsSiblings = false;
                            break;
                        }
                        if (tocItems[i].level < l) {
                            break;
                        }
                    }
                    if (!ancestorIsLastAmongItsSiblings) {
                        lines.push(
                          <div
                            key={`vline-ancestor-through-${l}`}
                            className="absolute top-0 bottom-0 border-l border-dotted border-gray-300 group-hover:border-blue-400 transition-colors duration-150"
                            style={{ left: `${ancestorVerticalLineX}px` }}
                          />
                        );
                    }
                  }
                }
                return lines;
              })()}
              
              <button
                onClick={() => handleTocClick(item)}
                className={`w-full text-left rounded hover:bg-gray-100 focus:outline-none focus:ring-1 focus:ring-blue-300 ${textClass} transition-colors duration-75`}
                title={item.title}
                style={{ paddingLeft: `${currentItemTextActualPaddingLeftPx}px`, lineHeight: '1.7' }}
              >
                <div className="flex items-center relative">
                  <span className={`block truncate`}>
                    {item.title}
                  </span>
                </div>
              </button>
            </div>
          );
        })}
      </nav>
      
      {tocItems.length > 0 && (
        <div className="mt-2 pt-2 border-t border-gray-200/60">
          {/* MODIFIED: 上边距和上内边距调整, 边框颜色调整 */}
          <p className="text-xs text-gray-500 text-center">
            {/* MODIFIED: 底部统计文本样式调整 */}
            共 {tocItems.length} 项
          </p>
        </div>
      )}
    </div>
  )
}

export default TableOfContents