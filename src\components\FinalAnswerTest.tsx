import React, { useState } from 'react'
import AssistantMessage from './chat/AssistantMessage'

// 工作流节点数据类型
interface WorkflowNode {
  id: string
  type: string
  title: string
  status: 'running' | 'success' | 'error'
  parallel_id?: string
  created_at: number
  error?: string
  inputs?: any
  outputs?: any
  process_data?: any
  displayCategory?: 'left' | 'right' | 'result' | 'none'
}

// 创建模拟节点的辅助函数
const createMockNode = (
  id: string, 
  title: string, 
  content: string, 
  status: 'running' | 'success' | 'error' = 'success'
): WorkflowNode => {
  const displayCategory = title.toUpperCase().includes('SHOW') ? 'left' as const :
                         title.toUpperCase().includes('DETAIL') ? 'right' as const :
                         title.toUpperCase().includes('RESULT') ? 'result' as const : 'none' as const

  return {
    id,
    type: 'llm',
    title,
    status,
    created_at: Date.now(),
    displayCategory,
    outputs: content ? { answer: content } : undefined
  }
}

const FinalAnswerTest: React.FC = () => {
  const [testScenario, setTestScenario] = useState<'mixed' | 'resultOnly' | 'noResult' | 'anchor' | 'autoScroll'>('autoScroll')
  const [simulationNodes, setSimulationNodes] = useState<WorkflowNode[]>([])
  const [isSimulating, setIsSimulating] = useState(false)

  // 测试场景数据
  const testScenarios = {
    mixed: {
      name: '混合节点测试',
      description: '包含 SHOW、DETAIL 和 RESULT 三种类型节点',
      nodes: [
        createMockNode('show-1', 'SHOW-用户需求分析', '正在分析用户需求...'),
        createMockNode('detail-1', 'DETAIL-详细处理过程', '这是详细的处理过程，包含技术细节和中间结果。'),
        createMockNode('result-1', 'RESULT-最终答案', '这是最终答案内容，应该显示在 FinalAnswer 组件中。'),
        createMockNode('other-1', '内部处理节点', '这是内部处理内容，不应该显示在任何地方。')
      ],
      expectedFinalAnswer: '这是最终答案内容，应该显示在 FinalAnswer 组件中。'
    },
    resultOnly: {
      name: '仅 Result 节点测试',
      description: '只包含 RESULT 类型节点',
      nodes: [
        createMockNode('result-1', 'RESULT-第一个答案', '第一个结果内容。'),
        createMockNode('result-2', 'RESULT-第二个答案', '第二个结果内容。')
      ],
      expectedFinalAnswer: '第一个结果内容。\n\n第二个结果内容。'
    },
    noResult: {
      name: '无 Result 节点测试',
      description: '不包含任何 RESULT 类型节点',
      nodes: [
        createMockNode('show-1', 'SHOW-步骤显示', '显示在左侧面板的内容。'),
        createMockNode('detail-1', 'DETAIL-详细信息', '显示在右侧面板的详细信息。'),
        createMockNode('other-1', '其他节点', '不显示的内容。')
      ],
      expectedFinalAnswer: '(不应该显示 FinalAnswer 组件)'
    },
    anchor: {
      name: '锚点功能测试',
      description: '测试左右面板锚点联动功能',
      nodes: [
        createMockNode('show-1', 'SHOW-数据收集', '正在收集相关数据...'),
        createMockNode('detail-1', 'DETAIL-数据分析', '这是数据分析的详细过程，包含多个步骤和中间结果。分析过程涉及数据清洗、特征提取、模型训练等多个环节。'),
        createMockNode('show-2', 'SHOW-模型训练', '正在训练机器学习模型...'),
        createMockNode('detail-2', 'DETAIL-模型评估', '模型评估结果显示准确率达到95%，召回率为92%，F1分数为93.5%。模型在测试集上表现良好，可以投入生产使用。'),
        createMockNode('show-3', 'SHOW-结果生成', '正在生成最终结果...'),
        createMockNode('detail-3', 'DETAIL-结果验证', '对生成的结果进行验证，确保输出质量符合预期标准。验证过程包括格式检查、内容审核、质量评分等多个维度。'),
        createMockNode('result-1', 'RESULT-最终报告', '基于数据分析和模型训练的结果，我们得出了以下结论...')
      ],
      expectedFinalAnswer: '基于数据分析和模型训练的结果，我们得出了以下结论...'
    },
    autoScroll: {
      name: '自动滚动测试',
      description: '测试左侧面板自动滚动功能',
      nodes: [
        createMockNode('show-1', 'SHOW-初始化', '系统初始化中...', 'success'),
        createMockNode('detail-1', 'DETAIL-配置加载', '正在加载系统配置文件...', 'success'),
        createMockNode('show-2', 'SHOW-数据预处理', '开始数据预处理...', 'success'),
        createMockNode('detail-2', 'DETAIL-数据清洗', '执行数据清洗操作，移除异常值和重复数据...', 'success'),
        createMockNode('show-3', 'SHOW-特征工程', '进行特征工程...', 'success'),
        createMockNode('detail-3', 'DETAIL-特征选择', '使用统计方法选择最优特征子集...', 'success'),
        createMockNode('show-4', 'SHOW-模型训练', '开始模型训练...', 'success'),
        createMockNode('detail-4', 'DETAIL-超参数优化', '正在进行超参数优化，寻找最佳参数组合...', 'success'),
        createMockNode('show-5', 'SHOW-模型评估', '准备模型评估...', 'success'),
        createMockNode('detail-5', 'DETAIL-性能测试', '在测试集上评估模型性能...', 'success'),
        createMockNode('show-6', 'SHOW-结果输出', '准备输出结果...', 'success'),
        createMockNode('detail-6', 'DETAIL-报告生成', '生成详细的分析报告...', 'success'),
        createMockNode('result-1', 'RESULT-最终结果', '模型训练完成，准确率达到96.5%')
      ],
      expectedFinalAnswer: '模型训练完成，准确率达到96.5%'
    }
  }

  const currentScenario = testScenarios[testScenario]

  // 模拟工作流执行的函数
  const simulateWorkflowExecution = () => {
    if (testScenario !== 'autoScroll') return

    setIsSimulating(true)
    const nodes = [...currentScenario.nodes]

    // 重置所有节点状态为初始状态（除了已完成的节点）
    const resetNodes = nodes.map((node, index) => ({
      ...node,
      status: 'success' as const, // 初始状态设为success，模拟已完成
      created_at: Date.now() + index * 100 // 给每个节点不同的时间戳
    }))

    setSimulationNodes(resetNodes)

    // 模拟逐个执行节点
    let currentIndex = 0
    const executeNextNode = () => {
      if (currentIndex >= nodes.length) {
        setIsSimulating(false)
        console.log('🏁 模拟执行完成')
        return
      }

      console.log(`🚀 开始执行节点 ${currentIndex}: ${nodes[currentIndex].id}`)

      // 设置当前节点为运行状态，其他节点保持之前的状态
      setSimulationNodes(prev => prev.map((node, index) => ({
        ...node,
        status: index === currentIndex ? 'running' as const :
                index < currentIndex ? 'success' as const : 'success' as const,
        created_at: Date.now() + index * 100
      })))

      // 2秒后完成当前节点并开始下一个
      setTimeout(() => {
        console.log(`✅ 完成节点 ${currentIndex}: ${nodes[currentIndex].id}`)

        setSimulationNodes(prev => prev.map((node, index) => ({
          ...node,
          status: index <= currentIndex ? 'success' as const : 'success' as const
        })))

        currentIndex++
        setTimeout(executeNextNode, 500) // 0.5秒间隔开始下一个节点
      }, 2000)
    }

    executeNextNode()
  }

  // 使用模拟节点数据或原始节点数据
  const activeNodes = testScenario === 'autoScroll' && simulationNodes.length > 0
    ? simulationNodes
    : currentScenario.nodes

  // 分类节点数据
  const leftPanelNodes = activeNodes.filter(n => n.displayCategory === 'left')
  const rightPanelNodes = activeNodes.filter(n => n.displayCategory === 'right')
  const resultNodes = activeNodes.filter(n => n.displayCategory === 'result')

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-6 text-center">FinalAnswer 组件测试</h1>
        
        {/* 测试场景选择 */}
        <div className="mb-8 bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">测试场景选择</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Object.entries(testScenarios).map(([key, scenario]) => (
              <button
                key={key}
                onClick={() => setTestScenario(key as any)}
                className={`p-4 rounded-lg border-2 transition-all ${
                  testScenario === key
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 bg-white hover:border-gray-300'
                }`}
              >
                <h3 className="font-medium mb-2">{scenario.name}</h3>
                <p className="text-sm text-gray-600">{scenario.description}</p>
              </button>
            ))}
          </div>
        </div>

        {/* 当前测试场景信息 */}
        <div className="mb-6 bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">当前测试：{currentScenario.name}</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div className="bg-blue-50 p-3 rounded">
              <h3 className="font-medium text-blue-800">左侧面板节点</h3>
              <p className="text-blue-600">数量: {leftPanelNodes.length}</p>
              {leftPanelNodes.map(n => (
                <div key={n.id} className="text-xs text-blue-500 mt-1">{n.title}</div>
              ))}
            </div>
            <div className="bg-green-50 p-3 rounded">
              <h3 className="font-medium text-green-800">右侧面板节点</h3>
              <p className="text-green-600">数量: {rightPanelNodes.length}</p>
              {rightPanelNodes.map(n => (
                <div key={n.id} className="text-xs text-green-500 mt-1">{n.title}</div>
              ))}
            </div>
            <div className="bg-purple-50 p-3 rounded">
              <h3 className="font-medium text-purple-800">结果节点</h3>
              <p className="text-purple-600">数量: {resultNodes.length}</p>
              {resultNodes.map(n => (
                <div key={n.id} className="text-xs text-purple-500 mt-1">{n.title}</div>
              ))}
            </div>
            <div className="bg-gray-50 p-3 rounded">
              <h3 className="font-medium text-gray-800">所有节点</h3>
              <p className="text-gray-600">总数: {currentScenario.nodes.length}</p>
            </div>
          </div>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded p-4">
            <h4 className="font-medium text-yellow-800 mb-2">预期 FinalAnswer 内容：</h4>
            <p className="text-yellow-700 text-sm whitespace-pre-wrap">{currentScenario.expectedFinalAnswer}</p>
          </div>

          {testScenario === 'anchor' && (
            <div className="bg-blue-50 border border-blue-200 rounded p-4 mt-4">
              <h4 className="font-medium text-blue-800 mb-2">🔗 锚点功能测试说明：</h4>
              <ul className="text-blue-700 text-sm space-y-1">
                <li>• <strong>左侧面板点击</strong>：点击左侧步骤项目，右侧面板会平滑滚动到对应节点，左侧项目会高亮显示（蓝色背景）</li>
                <li>• <strong>右侧面板滚动</strong>：滚动右侧面板时，左侧面板会自动高亮当前视口中心的对应节点</li>
                <li>• <strong>视觉反馈</strong>：活跃项目会有蓝色背景、边框和粗体文字，圆点会有蓝色光环效果</li>
                <li>• <strong>数据匹配</strong>：通过 data-node-id 属性进行左右面板的一对一匹配</li>
              </ul>
            </div>
          )}

          {testScenario === 'autoScroll' && (
            <div className="bg-green-50 border border-green-200 rounded p-4 mt-4">
              <h4 className="font-medium text-green-800 mb-2">📜 自动滚动功能测试说明：</h4>
              <ul className="text-green-700 text-sm space-y-1 mb-4">
                <li>• <strong>自动跟随执行</strong>：左侧面板会自动滚动到当前正在执行的工作流节点位置</li>
                <li>• <strong>平滑滚动</strong>：使用平滑滚动动画，将当前节点滚动到左侧面板的中心位置</li>
                <li>• <strong>用户滚动检测</strong>：当用户手动滚动左侧面板时，会暂停自动滚动1.5秒</li>
                <li>• <strong>状态触发</strong>：当节点状态从 'init' → 'running' 或 'running' → 'success/error' 时触发</li>
              </ul>
              <div className="flex gap-2">
                <button
                  onClick={simulateWorkflowExecution}
                  disabled={isSimulating}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                    isSimulating
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-green-600 text-white hover:bg-green-700 active:bg-green-800'
                  }`}
                >
                  {isSimulating ? '模拟执行中...' : '🚀 开始模拟工作流执行'}
                </button>
                {isSimulating && (
                  <div className="flex items-center text-green-600 text-sm">
                    <div className="animate-spin w-4 h-4 border-2 border-green-600 border-t-transparent rounded-full mr-2"></div>
                    观察左侧面板自动滚动效果
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* AssistantMessage 组件测试 */}
        <div className="bg-white rounded-lg shadow-lg">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold">实际渲染结果</h2>
            <p className="text-gray-600 text-sm mt-1">
              {testScenario === 'anchor'
                ? '测试左右面板锚点联动功能 - 点击左侧步骤或滚动右侧面板查看效果'
                : testScenario === 'autoScroll'
                ? '测试左侧面板自动滚动功能 - 点击上方按钮开始模拟工作流执行'
                : '检查 FinalAnswer 组件是否只显示 RESULT 类型节点的内容'
              }
            </p>
          </div>
          
          <AssistantMessage
            content="" // 故意设置为空，测试是否只显示 result 节点内容
            leftPanelNodes={leftPanelNodes}
            rightPanelNodes={rightPanelNodes}
            resultNodes={resultNodes}
            workflowNodes={activeNodes}
            workflowStatus={testScenario === 'autoScroll' && isSimulating ? "running" : "finished"}
            isGenerating={testScenario === 'autoScroll' && isSimulating}
          />
        </div>

        {/* 测试说明 */}
        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-blue-800 mb-3">测试验证要点</h2>
          <ul className="space-y-2 text-blue-700">
            <li className="flex items-start">
              <span className="text-blue-500 mr-2">•</span>
              <span><strong>混合节点测试</strong>：FinalAnswer 应该只显示 "这是最终答案内容，应该显示在 FinalAnswer 组件中。"</span>
            </li>
            <li className="flex items-start">
              <span className="text-blue-500 mr-2">•</span>
              <span><strong>仅 Result 节点测试</strong>：FinalAnswer 应该显示两个结果内容，用双换行符连接</span>
            </li>
            <li className="flex items-start">
              <span className="text-blue-500 mr-2">•</span>
              <span><strong>无 Result 节点测试</strong>：FinalAnswer 组件应该完全不显示（返回 null）</span>
            </li>
            <li className="flex items-start">
              <span className="text-blue-500 mr-2">•</span>
              <span>SHOW 和 DETAIL 类型节点的内容不应该出现在 FinalAnswer 中</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default FinalAnswerTest
