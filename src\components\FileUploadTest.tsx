import React, { useRef, useState } from 'react'

/**
 * 文件上传测试组件
 * 用于诊断文件选择功能问题
 */
const FileUploadTest: React.FC = () => {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [logs, setLogs] = useState<string[]>([])

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev, `[${timestamp}] ${message}`])
    console.log(message)
  }

  const handleButtonClick = () => {
    addLog('按钮被点击')
    if (fileInputRef.current) {
      addLog('触发文件选择对话框')
      fileInputRef.current.click()
    } else {
      addLog('ERROR: fileInputRef.current 为 null')
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    addLog(`文件选择事件触发，files.length: ${e.target.files?.length || 0}`)
    
    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files)
      setSelectedFiles(files)
      addLog(`成功选择 ${files.length} 个文件:`)
      files.forEach((file, index) => {
        addLog(`  ${index + 1}. ${file.name} (${file.type}, ${file.size} bytes)`)
      })
    } else {
      addLog('没有选择任何文件')
    }
  }

  const clearLogs = () => {
    setLogs([])
    setSelectedFiles([])
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">文件上传功能测试</h1>
      
      {/* 测试区域 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <h2 className="text-lg font-semibold mb-4">测试控件</h2>
        
        {/* 隐藏的文件输入 */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="*/*"
          onChange={handleFileChange}
          style={{ display: 'none' }}
        />
        
        {/* 测试按钮 */}
        <div className="space-x-4 mb-4">
          <button
            onClick={handleButtonClick}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            点击选择文件
          </button>
          
          <button
            onClick={clearLogs}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
          >
            清空日志
          </button>
        </div>

        {/* 直接可见的文件输入（对比测试） */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            直接文件输入（对比测试）:
          </label>
          <input
            type="file"
            multiple
            accept="*/*"
            onChange={(e) => {
              addLog(`直接输入选择了 ${e.target.files?.length || 0} 个文件`)
            }}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
        </div>
      </div>

      {/* 选择的文件列表 */}
      {selectedFiles.length > 0 && (
        <div className="bg-green-50 rounded-lg border border-green-200 p-6 mb-6">
          <h2 className="text-lg font-semibold mb-4 text-green-800">选择的文件</h2>
          <div className="space-y-2">
            {selectedFiles.map((file, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-white rounded border">
                <div>
                  <div className="font-medium">{file.name}</div>
                  <div className="text-sm text-gray-500">
                    类型: {file.type || '未知'} | 大小: {(file.size / 1024).toFixed(2)} KB
                  </div>
                </div>
                <div className="text-green-600">✓</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 日志区域 */}
      <div className="bg-gray-50 rounded-lg border border-gray-200 p-6">
        <h2 className="text-lg font-semibold mb-4">调试日志</h2>
        <div className="bg-black text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto">
          {logs.length === 0 ? (
            <div className="text-gray-500">暂无日志...</div>
          ) : (
            logs.map((log, index) => (
              <div key={index} className="mb-1">{log}</div>
            ))
          )}
        </div>
      </div>

      {/* 使用说明 */}
      <div className="mt-6 bg-blue-50 rounded-lg border border-blue-200 p-6">
        <h2 className="text-lg font-semibold mb-4 text-blue-800">测试说明</h2>
        <ul className="space-y-2 text-blue-700">
          <li>• 点击"点击选择文件"按钮测试隐藏input的文件选择功能</li>
          <li>• 使用"直接文件输入"测试浏览器原生文件选择功能</li>
          <li>• 查看调试日志了解文件选择过程中的详细信息</li>
          <li>• 如果隐藏input无法选择文件，但直接input可以，说明存在兼容性问题</li>
        </ul>
      </div>
    </div>
  )
}

export default FileUploadTest
