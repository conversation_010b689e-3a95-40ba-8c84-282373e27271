/**
 * 性能监控工具 - 用于监控组件渲染性能
 */

interface RenderMetrics {
  componentName: string;
  renderCount: number;
  lastRenderTime: number;
  totalRenderTime: number;
  averageRenderTime: number;
}

class PerformanceMonitor {
  private metrics: Map<string, RenderMetrics> = new Map();
  private renderStartTimes: Map<string, number> = new Map();

  /**
   * 开始监控组件渲染
   */
  startRender(componentName: string): void {
    this.renderStartTimes.set(componentName, performance.now());
  }

  /**
   * 结束监控组件渲染
   */
  endRender(componentName: string): void {
    const startTime = this.renderStartTimes.get(componentName);
    if (!startTime) return;

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    const existing = this.metrics.get(componentName);
    if (existing) {
      existing.renderCount++;
      existing.lastRenderTime = renderTime;
      existing.totalRenderTime += renderTime;
      existing.averageRenderTime = existing.totalRenderTime / existing.renderCount;
    } else {
      this.metrics.set(componentName, {
        componentName,
        renderCount: 1,
        lastRenderTime: renderTime,
        totalRenderTime: renderTime,
        averageRenderTime: renderTime
      });
    }

    this.renderStartTimes.delete(componentName);

    // 在开发环境下输出性能信息
    try {
      const isDevelopment = typeof window !== 'undefined' &&
        (window as any).__DEV__ !== false;

      if (isDevelopment) {
        const metric = this.metrics.get(componentName)!;
        console.log(`🔍 ${componentName} 渲染性能:`, {
          渲染次数: metric.renderCount,
          本次耗时: `${renderTime.toFixed(2)}ms`,
          平均耗时: `${metric.averageRenderTime.toFixed(2)}ms`,
          总耗时: `${metric.totalRenderTime.toFixed(2)}ms`
        });
      }
    } catch (e) {
      // 忽略环境检测错误
    }
  }

  /**
   * 获取组件性能指标
   */
  getMetrics(componentName?: string): RenderMetrics | RenderMetrics[] {
    if (componentName) {
      return this.metrics.get(componentName) || {
        componentName,
        renderCount: 0,
        lastRenderTime: 0,
        totalRenderTime: 0,
        averageRenderTime: 0
      };
    }
    return Array.from(this.metrics.values());
  }

  /**
   * 重置性能指标
   */
  reset(componentName?: string): void {
    if (componentName) {
      this.metrics.delete(componentName);
    } else {
      this.metrics.clear();
    }
  }

  /**
   * 生成性能报告
   */
  generateReport(): string {
    const allMetrics = Array.from(this.metrics.values());
    if (allMetrics.length === 0) {
      return '暂无性能数据';
    }

    let report = '\n📊 组件渲染性能报告\n';
    report += '='.repeat(50) + '\n';

    allMetrics
      .sort((a, b) => b.renderCount - a.renderCount)
      .forEach(metric => {
        report += `\n🔸 ${metric.componentName}\n`;
        report += `   渲染次数: ${metric.renderCount}\n`;
        report += `   平均耗时: ${metric.averageRenderTime.toFixed(2)}ms\n`;
        report += `   最后耗时: ${metric.lastRenderTime.toFixed(2)}ms\n`;
        report += `   总耗时: ${metric.totalRenderTime.toFixed(2)}ms\n`;
      });

    return report;
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor();

export default performanceMonitor;
