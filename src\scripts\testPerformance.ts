/**
 * 性能测试脚本 - 用于测试组件渲染性能优化效果
 */

import { performanceMonitor } from '../utils/performanceMonitor';

// 模拟工作流节点数据
const createMockWorkflowNodes = (count: number) => {
  return Array.from({ length: count }, (_, index) => ({
    id: `node-${index}`,
    type: index % 2 === 0 ? 'llm' : 'answer',
    title: `节点 ${index + 1}`,
    status: Math.random() > 0.5 ? 'success' : 'running' as const,
    created_at: Date.now() + index * 1000,
    displayCategory: index % 3 === 0 ? 'left' : index % 3 === 1 ? 'right' : 'result' as const,
    outputs: {
      answer: `这是节点 ${index + 1} 的输出内容。`.repeat(Math.floor(Math.random() * 10) + 1)
    }
  }));
};

// 模拟消息数据
const createMockMessage = (nodeCount: number = 10) => {
  const workflowNodes = createMockWorkflowNodes(nodeCount);
  
  return {
    id: `message-${Date.now()}`,
    type: 'assistant' as const,
    content: '这是一条测试消息内容。'.repeat(50),
    thinking: '这是思考过程内容。'.repeat(20),
    isGenerating: Math.random() > 0.5,
    workflowNodes,
    leftPanelNodes: workflowNodes.filter(n => n.displayCategory === 'left'),
    rightPanelNodes: workflowNodes.filter(n => n.displayCategory === 'right'),
    resultNodes: workflowNodes.filter(n => n.displayCategory === 'result'),
    searchResults: [],
    browsingResults: [],
    reviewContent: '评审内容。'.repeat(10),
    secondReviewContent: '复审内容。'.repeat(10),
    workflowStatus: 'running' as const,
    timestamp: new Date()
  };
};

// 性能测试函数
export const runPerformanceTest = () => {
  console.log('🚀 开始性能测试...');
  
  // 重置性能监控器
  performanceMonitor.reset();
  
  // 测试场景1：大量节点渲染
  console.log('\n📊 测试场景1：大量节点渲染性能');
  const largeMessage = createMockMessage(50);
  console.log(`创建了包含 ${largeMessage.workflowNodes.length} 个节点的消息`);
  
  // 测试场景2：频繁更新
  console.log('\n📊 测试场景2：频繁更新性能');
  const updateMessage = createMockMessage(20);
  
  // 模拟10次更新
  for (let i = 0; i < 10; i++) {
    // 模拟节点状态更新
    updateMessage.workflowNodes.forEach((node, index) => {
      if (Math.random() > 0.7) {
        node.outputs.answer += ` 更新${i + 1}`;
        node.status = Math.random() > 0.5 ? 'success' : 'running';
      }
    });
  }
  
  // 测试场景3：内存使用
  console.log('\n📊 测试场景3：内存使用测试');
  const messages = Array.from({ length: 100 }, () => createMockMessage(10));
  console.log(`创建了 ${messages.length} 条消息，总节点数: ${messages.reduce((sum, msg) => sum + msg.workflowNodes.length, 0)}`);
  
  // 输出性能报告
  setTimeout(() => {
    console.log(performanceMonitor.generateReport());
    
    // 性能建议
    console.log('\n💡 性能优化建议:');
    console.log('1. 使用 React.memo 包装子组件');
    console.log('2. 使用 useMemo 缓存计算结果');
    console.log('3. 使用 useCallback 缓存事件处理函数');
    console.log('4. 避免在渲染过程中创建新对象');
    console.log('5. 使用深度比较来优化 memo 效果');
    
    // 内存使用情况
    const perfMemory = (performance as any).memory;
    if (perfMemory) {
      console.log('\n🧠 内存使用情况:');
      console.log(`已使用: ${(perfMemory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
      console.log(`总分配: ${(perfMemory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
      console.log(`限制: ${(perfMemory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`);
    }
  }, 1000);
};

// 渲染性能基准测试
export const benchmarkRenderPerformance = () => {
  console.log('🎯 开始渲染性能基准测试...');
  
  const iterations = 1000;
  const nodeCount = 20;
  
  const startTime = performance.now();
  
  for (let i = 0; i < iterations; i++) {
    const message = createMockMessage(nodeCount);
    
    // 模拟组件渲染过程中的计算
    const processedNodes = message.workflowNodes.map(node => ({
      ...node,
      processed: true,
      displayName: `处理后的${node.title}`
    }));
    
    // 模拟深度比较
    const isEqual = JSON.stringify(message.workflowNodes) === JSON.stringify(processedNodes);
  }
  
  const endTime = performance.now();
  const totalTime = endTime - startTime;
  const averageTime = totalTime / iterations;
  
  console.log(`\n📈 基准测试结果:`);
  console.log(`总迭代次数: ${iterations}`);
  console.log(`每次节点数: ${nodeCount}`);
  console.log(`总耗时: ${totalTime.toFixed(2)}ms`);
  console.log(`平均耗时: ${averageTime.toFixed(4)}ms`);
  console.log(`每秒处理: ${(1000 / averageTime).toFixed(0)} 次`);
};

// 导出测试函数
export default {
  runPerformanceTest,
  benchmarkRenderPerformance,
  createMockMessage,
  createMockWorkflowNodes
};
