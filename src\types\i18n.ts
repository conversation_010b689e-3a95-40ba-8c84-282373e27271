import type { SupportedLanguage } from '../i18n'

/**
 * 国际化路由参数类型
 */
export interface I18nRouteParams {
  lang: SupportedLanguage
  appName: string
  sessionId?: string
}

/**
 * 语言配置类型
 */
export interface LanguageConfig {
  code: SupportedLanguage
  name: string
  flag: string
  locale: string
}

/**
 * 应用名称配置类型
 */
export type AppNameConfig = {
  [key: string]: {
    [K in SupportedLanguage]: string
  }
}

/**
 * 翻译键类型（用于类型安全的翻译）
 */
export type TranslationKey = 
  | `common.${string}`
  | `header.${string}`
  | `home.${string}`
  | `chat.${string}`
  | `sidebar.${string}`
  | `apps.${string}`
  | `services.${string}`
  | `subscription.${string}`
  | `errors.${string}`
  | `messages.${string}`
  | `workflow.${string}`
  | `fileUpload.${string}`
  | `auth.${string}`
  | `navigation.${string}`
  | `cases.${string}`
  | `payment.${string}`
  | `mobile.${string}`

/**
 * 翻译选项类型
 */
export interface TranslationOptions {
  count?: number
  context?: string
  defaultValue?: string
  [key: string]: any
}

/**
 * 国际化上下文类型
 */
export interface I18nContextType {
  currentLanguage: SupportedLanguage
  changeLanguage: (lang: SupportedLanguage) => Promise<void>
  t: (key: TranslationKey, options?: TranslationOptions) => string
  isLoading: boolean
  error: string | null
}

/**
 * 路由国际化配置类型
 */
export interface RouteI18nConfig {
  defaultLanguage: SupportedLanguage
  supportedLanguages: readonly SupportedLanguage[]
  fallbackLanguage: SupportedLanguage
  detectLanguageFromPath: boolean
  persistLanguagePreference: boolean
  redirectToDefaultLanguage: boolean
}

/**
 * 语言切换器组件属性类型
 */
export interface LanguageSwitcherProps {
  className?: string
  showFlag?: boolean
  showText?: boolean
  dropdownPosition?: 'left' | 'right'
  size?: 'small' | 'medium' | 'large'
  variant?: 'default' | 'minimal' | 'icon-only'
  onLanguageChange?: (lang: SupportedLanguage) => void
}

/**
 * 国际化路由包装器属性类型
 */
export interface I18nRouteWrapperProps {
  children: React.ReactNode
  fallbackComponent?: React.ComponentType
  loadingComponent?: React.ComponentType
}

/**
 * 翻译资源类型
 */
export interface TranslationResource {
  [key: string]: string | TranslationResource
}

/**
 * 翻译资源集合类型
 */
export type TranslationResources = {
  [K in SupportedLanguage]: {
    translation: TranslationResource
  }
}

/**
 * 国际化初始化选项类型
 */
export interface I18nInitOptions {
  defaultLanguage?: SupportedLanguage
  supportedLanguages?: readonly SupportedLanguage[]
  resources?: TranslationResources
  debug?: boolean
  fallbackLanguage?: SupportedLanguage
  detection?: {
    order?: string[]
    caches?: string[]
    lookupFromPathIndex?: number
    checkWhitelist?: boolean
  }
}

/**
 * 语言检测结果类型
 */
export interface LanguageDetectionResult {
  detectedLanguage: SupportedLanguage
  source: 'path' | 'localStorage' | 'navigator' | 'default'
  confidence: number
}

/**
 * 国际化错误类型
 */
export interface I18nError {
  code: string
  message: string
  details?: any
}

/**
 * 翻译缺失处理器类型
 */
export type MissingTranslationHandler = (
  language: SupportedLanguage,
  namespace: string,
  key: string,
  fallbackValue?: string
) => string

/**
 * 语言变更监听器类型
 */
export type LanguageChangeListener = (
  newLanguage: SupportedLanguage,
  oldLanguage: SupportedLanguage
) => void

/**
 * 国际化工具函数返回类型
 */
export interface I18nUtils {
  isValidLanguage: (lang: string) => lang is SupportedLanguage
  getValidLanguage: (lang?: string) => SupportedLanguage
  extractLanguageFromPath: (pathname: string) => SupportedLanguage
  extractAppNameFromPath: (pathname: string) => string
  extractSessionIdFromPath: (pathname: string) => string | null
  buildI18nPath: (lang: SupportedLanguage, appName: string, sessionId?: string) => string
  normalizePath: (pathname: string, defaultLang?: SupportedLanguage) => string
  getLanguageSwitchPath: (currentPath: string, targetLang: SupportedLanguage) => string
}
