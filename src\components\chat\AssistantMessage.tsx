import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react'
import Markdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { ChevronDownIcon, CheckIcon, SearchIcon, ExpandIcon, SourcesIcon } from '../icons/Icons';
import type { Components } from 'react-markdown'
import  AiResponse<PERSON>enderer  from '../AiResponseRenderer';
import { useRenderPerformance } from '../../hooks/useRenderPerformance';
import { useSimpleTranslation } from '../../i18n/simple-hooks';

// 添加自定义CSS动画样式
const customAnimations = `
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      max-height: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      max-height: 500px;
      transform: translateY(0);
    }
  }

  @keyframes slideUp {
    from {
      opacity: 1;
      max-height: 500px;
      transform: translateY(0);
    }
    to {
      opacity: 0;
      max-height: 0;
      transform: translateY(-10px);
    }
  }

  @keyframes pulse {
    0%, 100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.05);
      opacity: 0.8;
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .animate-fade-in {
    animation: fadeIn 0.4s ease-out forwards;
  }

  .animate-slide-down {
    animation: slideDown 0.5s ease-out forwards;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-in forwards;
  }

  .animate-gentle-pulse {
    animation: pulse 2s ease-in-out infinite;
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }

  .smooth-expand {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .smooth-collapse {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
`;

// 将样式注入到页面中
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = customAnimations;
  if (!document.head.querySelector('style[data-mobile-animations]')) {
    styleElement.setAttribute('data-mobile-animations', 'true');
    document.head.appendChild(styleElement);
  }
}


// 深度比较函数
const deepEqual = (a: any, b: any): boolean => {
  if (a === b) return true;
  if (a == null || b == null) return false;
  if (typeof a !== typeof b) return false;

  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false;
    return a.every((item, index) => deepEqual(item, b[index]));
  }

  if (typeof a === 'object') {
    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    if (keysA.length !== keysB.length) return false;
    return keysA.every(key => deepEqual(a[key], b[key]));
  }

  return false;
};

// 圆形头像组件
const Avatar: React.FC<{ name: string; type: 'professor' | 'doctor' }> = ({ name, type }) => {
  // 卡通头像SVG组件
  const ProfessorAvatar = () => (
    <svg width="24" height="24" viewBox="0 0 24 24" className="rounded-full">
      <defs>
        <clipPath id="circle">
          <circle cx="12" cy="12" r="12"/>
        </clipPath>
      </defs>
      <rect width="24" height="24" fill="#3B82F6" clipPath="url(#circle)"/>
      {/* 脸部 */}
      <circle cx="12" cy="10" r="6" fill="#FEF3C7"/>
      {/* 眼睛 */}
      <circle cx="10" cy="9" r="0.8" fill="#1F2937"/>
      <circle cx="14" cy="9" r="0.8" fill="#1F2937"/>
      {/* 眼镜 */}
      <circle cx="10" cy="9" r="1.5" fill="none" stroke="#374151" strokeWidth="0.3"/>
      <circle cx="14" cy="9" r="1.5" fill="none" stroke="#374151" strokeWidth="0.3"/>
      <line x1="11.5" y1="9" x2="12.5" y2="9" stroke="#374151" strokeWidth="0.3"/>
      {/* 嘴巴 */}
      <path d="M 10.5 11.5 Q 12 12.5 13.5 11.5" stroke="#1F2937" strokeWidth="0.4" fill="none"/>
      {/* 学士帽 */}
      <rect x="8" y="5" width="8" height="2" fill="#1F2937" rx="1"/>
      <polygon points="6,6 18,6 17,4 7,4" fill="#1F2937"/>
      <circle cx="17" cy="4.5" r="0.3" fill="#EF4444"/>
    </svg>
  )
  
  const DoctorAvatar = () => (
    <svg width="24" height="24" viewBox="0 0 24 24" className="rounded-full">
      <defs>
        <clipPath id="circle2">
          <circle cx="12" cy="12" r="12"/>
        </clipPath>
      </defs>
      <rect width="24" height="24" fill="#10B981" clipPath="url(#circle2)"/>
      {/* 脸部 */}
      <circle cx="12" cy="10" r="6" fill="#FEF3C7"/>
      {/* 眼睛 */}
      <circle cx="10" cy="9" r="0.8" fill="#1F2937"/>
      <circle cx="14" cy="9" r="0.8" fill="#1F2937"/>
      {/* 嘴巴 */}
      <path d="M 10.5 11.5 Q 12 12.5 13.5 11.5" stroke="#1F2937" strokeWidth="0.4" fill="none"/>
      {/* 领带 */}
      <polygon points="11.5,15 12.5,15 13,20 11,20" fill="#DC2626"/>
      <rect x="11" y="14" width="2" height="2" fill="#1F2937"/>
      {/* 西装领子 */}
      <path d="M 8 16 L 11 14 L 12 15 L 13 14 L 16 16 L 16 24 L 8 24 Z" fill="#1F2937"/>
      <path d="M 8 16 L 11 14 L 12 15" stroke="#374151" strokeWidth="0.3" fill="none"/>
      <path d="M 16 16 L 13 14 L 12 15" stroke="#374151" strokeWidth="0.3" fill="none"/>
    </svg>
  )
  
  return (
    <div className="inline-flex items-center justify-center mr-2 flex-shrink-0">
      {type === 'professor' ? <ProfessorAvatar /> : <DoctorAvatar />}
    </div>
  )
}

interface SearchResult {
  title: string
  type: string
  url: string
  description?: string
}

// 工作流节点数据
interface WorkflowNode {
  id: string
  type: string
  title: string
  status: 'running' | 'success' | 'error'
  parallel_id?: string
  created_at: number
  error?: string
  inputs?: any
  outputs?: any
  process_data?: any
  // 新增：节点分类字段
  displayCategory?: 'left' | 'right' | 'result' | 'none'
}

// 节点类型到中文名称的映射
const getNodeDisplayName = (nodeType: string, title?: string): string => {
  const typeMapping: Record<string, string> = {
    'start': '开始节点',
    'if-else': '条件判断',
    'document-extractor': '文档提取',
    'tool': '工具调用',
    'llm': 'AI分析',
    'answer': '生成回答',
    'assigner': '变量赋值',
    'variable-aggregator': '数据聚合',
    'question-classifier': '问题分类',
    'code': '代码执行',
    'template-transform': '模板转换',
    'http-request': 'HTTP请求',
    'parameter-extractor': '参数提取'
  }

  // 如果有自定义标题，优先使用标题
  if (title && title.trim()) {
    return title
  }

  // 否则使用类型映射
  return typeMapping[nodeType] || nodeType
}

interface AssistantMessageProps {
  content: string
  thinking?: string
  steps?: string[]
  isGenerating?: boolean
  searchResults?: SearchResult[]
  browsingResults?: string[]
  // 新增状态字段用于跟踪各个步骤的完成状态
  isSearching?: boolean
  isBrowsing?: boolean
  isThinking?: boolean
  isGeneratingAnswer?: boolean
  // 新增：评审状态字段
  isReviewing?: boolean
  reviewContent?: string
  // 新增：复审状态字段
  isSecondReviewing?: boolean
  secondReviewContent?: string
  // 新增：工作流相关字段
  workflowNodes?: WorkflowNode[]
  workflowStatus?: 'running' | 'finished'
  // 新增：分类显示的节点数据
  leftPanelNodes?: WorkflowNode[]    // 显示在左侧面板的节点
  rightPanelNodes?: WorkflowNode[]   // 显示在右侧面板的节点
  resultNodes?: WorkflowNode[]       // 显示在结果组件的节点
}


// 思考过程折叠状态小图标组件
const ThinkingCollapsedIcon: React.FC<{ isCollapsed: boolean }> = ({ isCollapsed }) => {
  if (!isCollapsed) return null;

  return (
    <ChevronDownIcon
      width={16}
      height={16}
      className="transform transition-all duration-200 text-gray-400"
    />
  );
}

// 现代光标组件
const ModernCursor: React.FC<{ color?: string; className?: string }> = ({
  color = 'bg-blue-600',
  className = ''
}) => (
  <span
    className={`inline-block w-0.5 h-5 ${color} ml-1 animate-cursor rounded-sm shadow-sm ${className}`}
    style={{
      filter: 'drop-shadow(0 0 2px rgba(59, 130, 246, 0.3))',
      transformOrigin: 'bottom'
    }}
  />
)

// 步骤项组件
const StepItem: React.FC<{
  step: { text: string; completed: boolean; onClick?: () => void; avatar?: React.ReactNode; nodeId?: string }
  index: number
  isActive?: boolean
  onStepClick?: (nodeId: string) => void
}> = ({ step, index, isActive = false, onStepClick }) => {

  const handleClick = () => {
    if (step.nodeId && onStepClick) {
      onStepClick(step.nodeId)
    } else if (step.onClick) {
      step.onClick()
    }
  }

  return (
    <div
      key={index}
      className={`step-item flex items-start relative z-10 transition-all duration-200 ${
        step.onClick || step.nodeId ? 'cursor-pointer rounded-lg p-2 -m-2' : ''
      } ${
        isActive ? '' : 'hover:bg-gray-50'
      }`}
      onClick={handleClick}
      data-node-id={step.nodeId}
    >
      <div className={`w-4 h-4 rounded-full flex-shrink-0 mr-4 mt-1 flex items-center justify-center transition-all duration-200 ${
        step.completed
          ? (step.onClick || step.nodeId)
            ? 'bg-gray-700 hover:scale-110 active:scale-95' : 'bg-gray-700'
          : 'bg-blue-500 animate-slow-pulse'
      } ${isActive ? '' : ''}`}>
        {step.completed ? (
          <CheckIcon
            width={10}
            height={10}
            stroke="white"
            strokeWidth={3}
            className="transition-transform duration-200 hover:scale-110"
          />
        ) : (
          <div className="w-2 h-2 bg-white rounded-full"></div>
        )}
      </div>
      <div className="flex items-center">
        {step.avatar && step.avatar}
        <span className={`text-sm leading-relaxed transition-all duration-200 ${
          step.completed ? 'text-gray-700' : 'text-gray-500 animate-slow-pulse'
        } ${(step.onClick || step.nodeId) ? 'hover:text-blue-700' : ''} ${
          isActive ? '' : 'font-medium'
        }`}>
          {step.text}
        </span>
      </div>
    </div>
  )
}

// 思考过程组件
const ThinkingSection: React.FC<{
  thinking: string
  isGenerating: boolean
  isCollapsed: boolean
  onToggleCollapse: () => void
  thinkingRef: React.RefObject<HTMLDivElement>
  t: (key: string, options?: any) => string
}> = ({ thinking, isGenerating, isCollapsed, onToggleCollapse, thinkingRef, t }) => (
  <div ref={thinkingRef} className="text-sm text-gray-700 leading-relaxed mb-6">
    <h3 className="font-medium text-gray-900 text-base mb-3">{t('chat.thinkingProcess')}</h3>
    <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
      isCollapsed ? 'max-h-0 opacity-0' : 'max-h-none opacity-100'
    }`}>
      <div className="bg-gray-50 rounded-lg p-4 border-l-4 border-blue-500 cursor-pointer" title={t('chat.clickToCollapse')} onClick={onToggleCollapse}>
        <div className="whitespace-pre-wrap text-gray-700">
          {thinking}
          {isGenerating && <ModernCursor color="bg-blue-500" />}
        </div>
      </div>
    </div>
    
    {isCollapsed && (
      <div 
        onClick={onToggleCollapse}
        className="bg-gray-100 rounded-lg p-3 border-l-4 border-gray-300 cursor-pointer hover:bg-gray-200 hover:border-gray-400 transition-all duration-200 group"
      >
        <div className="text-gray-500 text-xs group-hover:text-gray-700 transition-colors flex items-center gap-1">
          <span>{t('chat.thinkingCollapsed')} ({thinking.length} {t('chat.characters')})</span>
          <ThinkingCollapsedIcon isCollapsed={isCollapsed} />
        </div>
      </div>
    )}
  </div>
)

// 搜索结果项组件
const SearchResultItem: React.FC<{ result: SearchResult; index: number }> = ({ result, index }) => (
  <div key={index} className="relative inline-flex items-center px-3 py-1.5 bg-gray-50 rounded-full border hover:bg-gray-100 hover:border-gray-300 transition-all duration-200 cursor-pointer group">
    <div className="flex-shrink-0 mr-2">
      <div className={`w-4 h-4 rounded-full flex items-center justify-center transition-transform duration-200 hover:scale-110 ${
        result.type === 'docs' ? 'bg-blue-500' :
        result.type === 'github' ? 'bg-gray-800' : 'bg-green-500'
      }`}>
        <span className="text-white text-xs font-bold">
          {result.type === 'docs' ? 'D' : result.type === 'github' ? 'G' : 'W'}
        </span>
      </div>
    </div>
    <div className="flex-1 min-w-0">
      <div className="font-medium text-gray-900 text-xs truncate max-w-[200px] hover:text-blue-700 transition-colors">{result.title}</div>
    </div>
  </div>
)

// 搜索结果组件
const SearchResultsSection: React.FC<{
  searchResults: SearchResult[]
  searchResultsRef: React.RefObject<HTMLDivElement>
}> = ({ searchResults, searchResultsRef }) => (
  <div ref={searchResultsRef} className="text-sm text-gray-700 leading-relaxed mb-6">
    <h3 className="font-medium text-gray-900 text-base mb-3">搜索结果</h3>
    <div className="flex flex-wrap gap-2">
      {searchResults.map((result, index) => (
        <SearchResultItem key={index} result={result} index={index} />
      ))}
    </div>
  </div>
)

// 浏览结果组件
const BrowsingResultsSection: React.FC<{
  browsingResults: string[]
  isGenerating: boolean
  isCollapsed: boolean
  onToggleCollapse: () => void
  browsingResultsRef: React.RefObject<HTMLDivElement>
  t: (key: string, options?: any) => string
}> = ({ browsingResults, isGenerating, isCollapsed, onToggleCollapse, browsingResultsRef, t }) => (
  <div ref={browsingResultsRef} className="text-sm text-gray-700 leading-relaxed mb-6">
    <h3 className="font-medium text-gray-900 text-base mb-3">{t('chat.browsingResults')}</h3>
    <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
      isCollapsed ? 'max-h-0 opacity-0' : 'max-h-none opacity-100'
    }`}>
      <div className="bg-green-50 rounded-lg p-4 border-l-4 border-green-500 cursor-pointer" title={t('chat.clickToCollapse')} onClick={onToggleCollapse}>
        <div className="space-y-2">
          {browsingResults.map((result, index) => (
            <div key={index} className="text-gray-700">
              {result}
              {isGenerating && index === browsingResults.length - 1 && (
                <ModernCursor color="bg-green-500" />
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
    
    {isCollapsed && (
      <div 
        onClick={onToggleCollapse}
        className="bg-gray-100 rounded-lg p-3 border-l-4 border-gray-300 cursor-pointer hover:bg-gray-200 hover:border-gray-400 transition-all duration-200 group"
      >
        <div className="text-gray-500 text-xs group-hover:text-gray-700 transition-colors flex items-center gap-1">
          <span>{t('chat.browsingCollapsed')} ({browsingResults.join('').length} {t('chat.characters')})</span>
          <ThinkingCollapsedIcon isCollapsed={isCollapsed} />
        </div>
      </div>
    )}
  </div>
)

// 评审过程组件
const ReviewSection: React.FC<{
  reviewContent: string
  isGenerating: boolean
  isCollapsed: boolean
  onToggleCollapse: () => void
  reviewRef: React.RefObject<HTMLDivElement>
  t: (key: string, options?: any) => string
}> = ({ reviewContent, isGenerating, isCollapsed, onToggleCollapse, reviewRef, t }) => (
  <div ref={reviewRef} className="text-sm text-gray-700 leading-relaxed mb-6">
    <h3 className="font-medium text-gray-900 text-base mb-3 flex items-center">
      <Avatar name="高博士" type="professor" />
      高博士{t('chat.reviewingFinalResult')}
    </h3>
    <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
      isCollapsed ? 'max-h-0 opacity-0' : 'max-h-none opacity-100'
    }`}>
      <div className="bg-purple-50 rounded-lg p-4 border-l-4 border-purple-500 cursor-pointer" title={t('chat.clickToCollapse')} onClick={onToggleCollapse}>
        <div className="whitespace-pre-wrap text-gray-700">
          {reviewContent}
          {isGenerating && <ModernCursor color="bg-purple-500" />}
        </div>
      </div>
    </div>
    
    {isCollapsed && (
      <div 
        onClick={onToggleCollapse}
        className="bg-purple-100 rounded-lg p-3 border-l-4 border-purple-300 cursor-pointer hover:bg-purple-200 hover:border-purple-400 transition-all duration-200 group"
      >
        <div className="text-purple-600 text-xs group-hover:text-purple-700 transition-colors flex items-center gap-1">
          <span>{t('chat.reviewCollapsed')} ({reviewContent.length} {t('chat.characters')})</span>
          <ThinkingCollapsedIcon isCollapsed={isCollapsed} />
        </div>
      </div>
    )}
  </div>
)

// 张博士复审过程组件
const SecondReviewSection: React.FC<{
  secondReviewContent: string
  isGenerating: boolean
  isCollapsed: boolean
  onToggleCollapse: () => void
  secondReviewRef: React.RefObject<HTMLDivElement>
  t: (key: string, options?: any) => string
}> = ({ secondReviewContent, isGenerating, isCollapsed, onToggleCollapse, secondReviewRef, t }) => (
  <div ref={secondReviewRef} className="text-sm text-gray-700 leading-relaxed mb-6">
    <h3 className="font-medium text-gray-900 text-base mb-3 flex items-center">
      <Avatar name="张博士" type="doctor" />
      张博士{t('chat.secondReviewingFinalResult')}
    </h3>
    <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
      isCollapsed ? 'max-h-0 opacity-0' : 'max-h-none opacity-100'
    }`}>
      <div className="bg-orange-50 rounded-lg p-4 border-l-4 border-orange-500 cursor-pointer" title={t('chat.clickToCollapse')} onClick={onToggleCollapse}>
        <div className="whitespace-pre-wrap text-gray-700">
          {secondReviewContent}
          {isGenerating && <ModernCursor color="bg-orange-500" />}
        </div>
      </div>
    </div>
    
    {isCollapsed && (
      <div 
        onClick={onToggleCollapse}
        className="bg-orange-100 rounded-lg p-3 border-l-4 border-orange-300 cursor-pointer hover:bg-orange-200 hover:border-orange-400 transition-all duration-200 group"
      >
        <div className="text-orange-600 text-xs group-hover:text-orange-700 transition-colors flex items-center gap-1">
          <span>{t('chat.secondReviewCollapsed')} ({secondReviewContent.length} {t('chat.characters')})</span>
          <ThinkingCollapsedIcon isCollapsed={isCollapsed} />
        </div>
      </div>
    )}
  </div>
)

// 单个工作流节点组件 - 支持智能折叠，模拟其他组件样式
const WorkflowNodeItem: React.FC<{
  node: WorkflowNode
  isCollapsed: boolean
  onToggleCollapse: () => void
  onNodeClick?: (nodeId: string) => void
  t: (key: string, options?: any) => string
}> = React.memo(({ node, isCollapsed, onToggleCollapse, onNodeClick, t }) => {
  // 智能判断内容是否需要折叠
  const shouldShowCollapse = useMemo(() => {
    const content = node?.outputs?.answer || ''
    const characterLimit = 200 // 字符数限制
    const lineLimit = 3 // 行数限制

    if (!content) return false

    // 检查字符数
    if (content.length > characterLimit) return true

    // 检查行数
    const lines = content.split('\n').length
    if (lines > lineLimit) return true

    return false
  }, [node?.outputs?.answer])

  const formatNodeData = (data: any) => {
    if (!data) return null

    try {
      // 如果是字符串，尝试解析为JSON
      const parsedData = typeof data === 'string' ? JSON.parse(data) : data
      return JSON.stringify(parsedData, null, 2)
    } catch (e) {
      // 如果解析失败，直接返回原始数据
      return typeof data === 'string' ? data : JSON.stringify(data, null, 2)
    }
  }

  const getDataSize = () => {
    let totalSize = 0
    if (node.outputs) totalSize += JSON.stringify(node.outputs).length
    if (node.process_data) totalSize += JSON.stringify(node.process_data).length
    if (node.inputs) totalSize += JSON.stringify(node.inputs).length
    return totalSize
  }

  // 根据节点ID生成随机颜色主题
  const getNodeColorTheme = (nodeId: string) => {
    const colors = [
      {
        name: 'blue',
        dot: 'bg-blue-500',
        bg: 'bg-blue-50',
        border: 'border-blue-500',
        borderLight: 'border-blue-200',
        text: 'text-blue-700',
        collapsedBg: 'bg-blue-100',
        collapsedBorder: 'border-blue-300',
        collapsedText: 'text-blue-600',
        hoverBg: 'hover:bg-blue-200',
        hoverBorder: 'hover:border-blue-400'
      },
      {
        name: 'green',
        dot: 'bg-green-500',
        bg: 'bg-green-50',
        border: 'border-green-500',
        borderLight: 'border-green-200',
        text: 'text-green-700',
        collapsedBg: 'bg-green-100',
        collapsedBorder: 'border-green-300',
        collapsedText: 'text-green-600',
        hoverBg: 'hover:bg-green-200',
        hoverBorder: 'hover:border-green-400'
      },
      {
        name: 'purple',
        dot: 'bg-purple-500',
        bg: 'bg-purple-50',
        border: 'border-purple-500',
        borderLight: 'border-purple-200',
        text: 'text-purple-700',
        collapsedBg: 'bg-purple-100',
        collapsedBorder: 'border-purple-300',
        collapsedText: 'text-purple-600',
        hoverBg: 'hover:bg-purple-200',
        hoverBorder: 'hover:border-purple-400'
      },
      {
        name: 'orange',
        dot: 'bg-orange-500',
        bg: 'bg-orange-50',
        border: 'border-orange-500',
        borderLight: 'border-orange-200',
        text: 'text-orange-700',
        collapsedBg: 'bg-orange-100',
        collapsedBorder: 'border-orange-300',
        collapsedText: 'text-orange-600',
        hoverBg: 'hover:bg-orange-200',
        hoverBorder: 'hover:border-orange-400'
      },
      {
        name: 'red',
        dot: 'bg-red-500',
        bg: 'bg-red-50',
        border: 'border-red-500',
        borderLight: 'border-red-200',
        text: 'text-red-700',
        collapsedBg: 'bg-red-100',
        collapsedBorder: 'border-red-300',
        collapsedText: 'text-red-600',
        hoverBg: 'hover:bg-red-200',
        hoverBorder: 'hover:border-red-400'
      },
      {
        name: 'indigo',
        dot: 'bg-indigo-500',
        bg: 'bg-indigo-50',
        border: 'border-indigo-500',
        borderLight: 'border-indigo-200',
        text: 'text-indigo-700',
        collapsedBg: 'bg-indigo-100',
        collapsedBorder: 'border-indigo-300',
        collapsedText: 'text-indigo-600',
        hoverBg: 'hover:bg-indigo-200',
        hoverBorder: 'hover:border-indigo-400'
      },
      {
        name: 'pink',
        dot: 'bg-pink-500',
        bg: 'bg-pink-50',
        border: 'border-pink-500',
        borderLight: 'border-pink-200',
        text: 'text-pink-700',
        collapsedBg: 'bg-pink-100',
        collapsedBorder: 'border-pink-300',
        collapsedText: 'text-pink-600',
        hoverBg: 'hover:bg-pink-200',
        hoverBorder: 'hover:border-pink-400'
      },
      {
        name: 'teal',
        dot: 'bg-teal-500',
        bg: 'bg-teal-50',
        border: 'border-teal-500',
        borderLight: 'border-teal-200',
        text: 'text-teal-700',
        collapsedBg: 'bg-teal-100',
        collapsedBorder: 'border-teal-300',
        collapsedText: 'text-teal-600',
        hoverBg: 'hover:bg-teal-200',
        hoverBorder: 'hover:border-teal-400'
      }
    ]

    // 使用节点ID生成一致的随机索引
    let hash = 0
    for (let i = 0; i < nodeId.length; i++) {
      const char = nodeId.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    const index = Math.abs(hash) % colors.length
    return colors[index]
  }

  const colorTheme = getNodeColorTheme(node.id)
      console.log(node?.outputs?.answer)

  return (
    <div className="text-sm text-gray-700 leading-relaxed !mb-0 !mt-2" data-node-id={node.id}>
      <h3
        className="font-medium text-gray-900 text-base mb-3 flex items-center cursor-pointer hover:text-blue-600 transition-colors"
        onClick={() => onNodeClick?.(node.id)}
        title={t('chat.clickToSync')}
      >
        <div className={`w-3 h-3 ${colorTheme.dot} rounded-full mr-2`}></div>
        {getNodeDisplayName(node.type, node.title)}
      </h3>
      {/* 只有当节点有输出内容时才显示内容区域 */}
      {node?.outputs?.answer && node.outputs.answer.trim().length > 0 && (
        shouldShowCollapse ? (
          // 长内容：支持折叠功能
          <>
            <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
              isCollapsed ? 'max-h-0 opacity-0' : 'max-h-none opacity-100'
            }`}>
              <div className={`${colorTheme.bg} rounded-lg p-4 border-l-4 ${colorTheme.border} cursor-pointer`}
                   title='点击折叠' onClick={onToggleCollapse}>
                <div className="text-gray-700 space-y-4">
                  <div>
                    <AiResponseRenderer key="ai-response" content={node?.outputs?.answer} />
                    {node?.status === 'running' && <ModernCursor color="bg-blue-500" />}
                  </div>
                </div>
              </div>
            </div>

            {isCollapsed && (
              <div
                onClick={onToggleCollapse}
                className={`${colorTheme.collapsedBg} rounded-lg p-3 border-l-4 ${colorTheme.collapsedBorder} cursor-pointer ${colorTheme.hoverBg} ${colorTheme.hoverBorder} transition-all duration-200 group`}
              >
                <div className={`${colorTheme.collapsedText} text-xs transition-colors flex items-center gap-1`}>
                  <span>{t('chat.nodeDataCollapsed')} ({getDataSize()} {t('chat.characters')})</span>
                  <ThinkingCollapsedIcon isCollapsed={isCollapsed} />
                </div>
              </div>
            )}
          </>
        ) : (
          // 短内容：直接显示，无背景样式
          <div className="text-gray-700">
            <div>
              <AiResponseRenderer key="ai-response" content={node.outputs.answer} />
              {node?.status === 'running' && <ModernCursor color="bg-blue-500" />}
            </div>
          </div>
        )
      )}
    </div>
  )
}, (prevProps, nextProps) => {
  // 自定义比较函数，只在关键属性变化时重新渲染
  return (
    prevProps.node.id === nextProps.node.id &&
    prevProps.node.status === nextProps.node.status &&
    deepEqual(prevProps.node.outputs, nextProps.node.outputs) &&
    prevProps.isCollapsed === nextProps.isCollapsed
  );
});

// 工作流节点数据显示组件
const WorkflowNodeSection: React.FC<{
  workflowNodes: WorkflowNode[]
  workflowNodeRef: React.RefObject<HTMLDivElement>
  onNodeClick?: (nodeId: string) => void
  t: (key: string, options?: any) => string
}> = React.memo(({ workflowNodes, workflowNodeRef, onNodeClick, t }) => {
  if (!workflowNodes || workflowNodes.length === 0) return null

  // 过滤出需要显示的节点：只显示 displayCategory 为 'right' 的节点
  const nodesWithData = workflowNodes.filter(node => {


    // 只显示 displayCategory 为 'right' 的节点
    const shouldShow = node.displayCategory === 'right'|| node.displayCategory === 'left'

    return shouldShow
  })

  if (nodesWithData.length === 0) return null

  // 为每个节点管理折叠状态（只对长内容节点）
  const [nodeCollapseStates, setNodeCollapseStates] = useState<Record<string, boolean>>({})

  // 判断节点是否需要折叠功能
  const shouldNodeShowCollapse = useCallback((node: WorkflowNode) => {
    const content = node?.outputs?.answer || ''
    const characterLimit = 200 // 字符数限制
    const lineLimit = 3 // 行数限制

    if (!content) return false

    // 检查字符数
    if (content.length > characterLimit) return true

    // 检查行数
    const lines = content.split('\n').length
    if (lines > lineLimit) return true

    return false
  }, [])

  const toggleNodeCollapse = (nodeId: string) => {
    setNodeCollapseStates(prev => ({
      ...prev,
      [nodeId]: !prev[nodeId]
    }))
  }

  return (
    <div ref={workflowNodeRef} className="text-sm text-gray-700 leading-relaxed mb-6">
      <h3 className="font-medium text-gray-900 text-base mb-3">Thinking</h3>
      <div className="space-y-4">
        {nodesWithData.map((node) => {
          const needsCollapse = shouldNodeShowCollapse(node)
          console.log(node)
          return (
            <WorkflowNodeItem
              key={node.id}
              node={node}
              isCollapsed={needsCollapse ? (nodeCollapseStates[node.id] || false) : false}
              onToggleCollapse={() => needsCollapse && toggleNodeCollapse(node.id)}
              onNodeClick={onNodeClick}
              t={t}
            />
          )
        })}
      </div>
    </div>
  )
}, (prevProps, nextProps) => {
  // 深度比较工作流节点数组
  return deepEqual(prevProps.workflowNodes, nextProps.workflowNodes);
});

// 独立的左侧面板组件 - 关键优化点
interface LeftPanelProps {
  dynamicSteps: Array<{ text: string; completed: boolean; onClick?: () => void; avatar?: React.ReactNode; nodeId?: string }>;
  completedSteps: number;
  totalSteps: number;
  sourcesCount: number;
  isExpanded: boolean;
  isGenerating: boolean;
  onExpandToggle: () => void;
  // 新增：锚点功能相关props
  activeNodeId?: string;
  onStepClick?: (nodeId: string) => void;
  // 新增：自动滚动功能相关props
  currentRunningNodeId?: string;
  leftPanelRef?: React.RefObject<HTMLDivElement>;
}

const LeftPanelComponentBase: React.FC<LeftPanelProps> = ({
  dynamicSteps,
  completedSteps,
  totalSteps,
  sourcesCount,
  isExpanded,
  isGenerating,
  onExpandToggle,
  activeNodeId,
  onStepClick,
  currentRunningNodeId,
  leftPanelRef
}) => {
  // 国际化
  const { t } = useSimpleTranslation();

  // 调试：监控左侧面板渲染次数
  console.log('🔄 LeftPanelComponent 重新渲染', {
    totalSteps,
    completedSteps,
    isGenerating,
    dynamicStepsLength: dynamicSteps.length
  });

  // 用户滚动状态管理
  const [isUserScrollingLeft, setIsUserScrollingLeft] = useState(false)
  const leftScrollTimeoutRef = useRef<number | null>(null)

  // 处理左侧面板滚动事件
  const handleLeftPanelScroll = useCallback(() => {
    setIsUserScrollingLeft(true)

    // 清除之前的定时器
    if (leftScrollTimeoutRef.current) {
      clearTimeout(leftScrollTimeoutRef.current)
    }

    // 1.5秒后重置用户滚动状态
    leftScrollTimeoutRef.current = setTimeout(() => {
      setIsUserScrollingLeft(false)
    }, 1500)
  }, [])

  // 绑定左侧面板滚动事件监听器
  useEffect(() => {
    const leftPanelContainer = leftPanelRef?.current?.querySelector('.left-panel-content') as HTMLElement
    if (!leftPanelContainer) return

    leftPanelContainer.addEventListener('scroll', handleLeftPanelScroll, { passive: true })

    return () => {
      leftPanelContainer.removeEventListener('scroll', handleLeftPanelScroll)
      if (leftScrollTimeoutRef.current) {
        clearTimeout(leftScrollTimeoutRef.current)
      }
    }
  }, [handleLeftPanelScroll, leftPanelRef])

  // 自动滚动到当前执行节点的逻辑
  useEffect(() => {
    if (!currentRunningNodeId || !leftPanelRef?.current || isUserScrollingLeft) return

    // 添加小延迟确保DOM完全渲染
    const scrollTimeout = setTimeout(() => {
      if (!leftPanelRef?.current) return

      // 查找当前执行节点对应的步骤元素
      const targetStepElement = leftPanelRef.current.querySelector(`[data-node-id="${currentRunningNodeId}"]`) as HTMLElement
      if (!targetStepElement) {
        console.log('🔍 未找到目标节点元素:', currentRunningNodeId)
        return
      }

      const leftPanelContainer = leftPanelRef.current.querySelector('.left-panel-content') as HTMLElement
      if (!leftPanelContainer) {
        console.log('🔍 未找到左侧面板容器')
        return
      }

      // 获取容器和目标元素的位置信息
      const containerHeight = leftPanelContainer.clientHeight
      const containerScrollTop = leftPanelContainer.scrollTop
      const containerScrollHeight = leftPanelContainer.scrollHeight

      // 计算目标元素在容器中的位置
      let targetOffsetTop = 0
      let currentElement = targetStepElement

      // 向上遍历DOM树，累计offsetTop直到找到滚动容器
      while (currentElement && currentElement !== leftPanelContainer) {
        targetOffsetTop += currentElement.offsetTop
        currentElement = currentElement.offsetParent as HTMLElement
      }

      const targetHeight = targetStepElement.offsetHeight

      // 将目标元素滚动到容器的中心位置
      const scrollTop = targetOffsetTop - (containerHeight / 2) + (targetHeight / 2)

      // 确保滚动位置在有效范围内
      const maxScrollTop = containerScrollHeight - containerHeight
      const finalScrollTop = Math.max(0, Math.min(scrollTop, maxScrollTop))

      // 平滑滚动到目标位置
      leftPanelContainer.scrollTo({
        top: finalScrollTop,
        behavior: 'smooth'
      })

      console.log('🎯 左侧面板自动滚动到节点:', currentRunningNodeId, {
        targetOffsetTop,
        scrollTop,
        finalScrollTop,
        containerHeight,
        maxScrollTop,
        scrollHeight: containerScrollHeight,
        currentScrollTop: containerScrollTop
      })
    }, 100) // 100ms延迟

    return () => clearTimeout(scrollTimeout)
  }, [currentRunningNodeId, leftPanelRef, isUserScrollingLeft])

  return (
  <div ref={leftPanelRef} className={`${isExpanded ? 'w-[312px]' : 'w-[218px]'} flex-shrink-0 bg-gray-50 border-r border-gray-200 flex flex-col h-full`}>
    <div className="left-panel-content p-6 flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent hover:scrollbar-thumb-gray-500 scroll-smooth">
      <div className="flex items-center mb-4">
        <SearchIcon
          width={18}
          height={18}
          className="text-gray-600 mr-3"
        />
        <span className="text-gray-900 font-medium text-base">
          {isGenerating ? t('chat.generating') : t('workflow.status.finished')}
        </span>
      </div>
      <div className="text-gray-500 text-sm mb-4">
        {completedSteps}/{totalSteps} steps{sourcesCount > 0 && ` • ${sourcesCount} sources`}
      </div>

      <div className="space-y-6 relative">
        {dynamicSteps.map((step, index) => (
          <div key={index} className="relative">
            {/* 连接线：只在非最后一个节点显示，确保连续且不超出 */}
            {index < dynamicSteps.length - 1 && (
              <div className="absolute left-[8px] top-[12px] bottom-[-36px] w-0.5 bg-gray-300"></div>
            )}
            <StepItem
              step={step}
              index={index}
              isActive={step.nodeId === activeNodeId}
              onStepClick={onStepClick}
            />
          </div>
        ))}
      </div>
    </div>

    <div className="p-6 pt-5 border-t border-gray-200 flex-shrink-0">
      <div className="flex items-center justify-between text-gray-500 hover:text-gray-700 cursor-pointer">
        <div
          className="flex items-center hover:text-blue-600"
          onClick={onExpandToggle}
        >
          <ExpandIcon
            width={14}
            height={14}
            className="mr-2"
          />
          <span className="text-sm font-medium">Expand</span>
        </div>
      </div>
    </div>
  </div>
  );
};

const LeftPanelComponent = React.memo(LeftPanelComponentBase, (prevProps, nextProps) => {
  // 自定义比较函数 - 只在关键数据变化时重新渲染
  const isEqual = (
    prevProps.completedSteps === nextProps.completedSteps &&
    prevProps.totalSteps === nextProps.totalSteps &&
    prevProps.sourcesCount === nextProps.sourcesCount &&
    prevProps.isExpanded === nextProps.isExpanded &&
    prevProps.isGenerating === nextProps.isGenerating &&
    prevProps.activeNodeId === nextProps.activeNodeId &&
    prevProps.currentRunningNodeId === nextProps.currentRunningNodeId &&
    deepEqual(prevProps.dynamicSteps, nextProps.dynamicSteps)
  );

  // 调试：记录比较结果
  console.log('🔍 LeftPanelComponent 比较结果:', isEqual, {
    completedSteps: prevProps.completedSteps === nextProps.completedSteps,
    totalSteps: prevProps.totalSteps === nextProps.totalSteps,
    isGenerating: prevProps.isGenerating === nextProps.isGenerating,
    dynamicStepsEqual: deepEqual(prevProps.dynamicSteps, nextProps.dynamicSteps)
  });

  return isEqual;
});

// 独立的右侧面板组件 - 关键优化点
interface RightPanelProps {
  thinking?: string;
  isGenerating: boolean;
  isThinkingCollapsed: boolean;
  onThinkingToggle: () => void;
  thinkingRef: React.RefObject<HTMLDivElement>;
  searchResults: any[];
  searchResultsRef: React.RefObject<HTMLDivElement>;
  browsingResults: string[];
  isBrowsing: boolean;
  isBrowsingCollapsed: boolean;
  onBrowsingToggle: () => void;
  browsingResultsRef: React.RefObject<HTMLDivElement>;
  workflowNodes: WorkflowNode[];
  rightPanelNodes: WorkflowNode[];
  workflowNodeRef: React.RefObject<HTMLDivElement>;
  onNodeClick?: (nodeId: string) => void;
  reviewContent?: string;
  isReviewing: boolean;
  isReviewCollapsed: boolean;
  onReviewToggle: () => void;
  reviewRef: React.RefObject<HTMLDivElement>;
  secondReviewContent?: string;
  isSecondReviewing: boolean;
  isSecondReviewCollapsed: boolean;
  onSecondReviewToggle: () => void;
  secondReviewRef: React.RefObject<HTMLDivElement>;
  rightPanelRef: React.RefObject<HTMLDivElement>;
  t: (key: string, options?: any) => string;
}

const RightPanelComponentBase: React.FC<RightPanelProps> = ({
  thinking,
  isGenerating,
  isThinkingCollapsed,
  onThinkingToggle,
  thinkingRef,
  searchResults,
  searchResultsRef,
  browsingResults,
  isBrowsing,
  isBrowsingCollapsed,
  onBrowsingToggle,
  browsingResultsRef,
  workflowNodes,
  rightPanelNodes,
  workflowNodeRef,
  onNodeClick,
  reviewContent,
  isReviewing,
  isReviewCollapsed,
  onReviewToggle,
  reviewRef,
  secondReviewContent,
  isSecondReviewing,
  isSecondReviewCollapsed,
  onSecondReviewToggle,
  secondReviewRef,
  rightPanelRef,
  t
}) => {
  // 调试：监控右侧面板渲染次数
  console.log('🔄 RightPanelComponent 重新渲染', {
    workflowNodesLength: workflowNodes?.length || 0,
    rightPanelNodesLength: rightPanelNodes?.length || 0,
    isGenerating,
    hasThinking: !!thinking
  });

  return (
  <div ref={rightPanelRef} className="flex-1 p-6 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent hover:scrollbar-thumb-gray-500 scroll-smooth">
    {thinking && (
      <ThinkingSection
        thinking={thinking}
        isGenerating={isGenerating}
        isCollapsed={isThinkingCollapsed}
        onToggleCollapse={onThinkingToggle}
        thinkingRef={thinkingRef}
        t={t}
      />
    )}

    {searchResults.length > 0 && (
      <SearchResultsSection
        searchResults={searchResults}
        searchResultsRef={searchResultsRef}
      />
    )}

    {browsingResults.length > 0 && (
      <BrowsingResultsSection
        browsingResults={browsingResults}
        isGenerating={isBrowsing}
        isCollapsed={isBrowsingCollapsed}
        onToggleCollapse={onBrowsingToggle}
        browsingResultsRef={browsingResultsRef}
        t={t}
      />
    )}

    {/* 工作流节点数据组件 - 优先显示rightPanelNodes */}
    {((rightPanelNodes && rightPanelNodes.length > 0) || (workflowNodes && workflowNodes.length > 0)) && (
      <WorkflowNodeSection
        workflowNodes={rightPanelNodes && rightPanelNodes.length > 0 ? rightPanelNodes : workflowNodes}
        workflowNodeRef={workflowNodeRef}
        onNodeClick={onNodeClick}
        t={t}
      />
    )}

    {/* 评审组件 */}
    {reviewContent && (
      <ReviewSection
        reviewContent={reviewContent}
        isGenerating={isReviewing}
        isCollapsed={isReviewCollapsed}
        onToggleCollapse={onReviewToggle}
        reviewRef={reviewRef}
        t={t}
      />
    )}

    {/* 复审组件 */}
    {secondReviewContent && (
      <SecondReviewSection
        secondReviewContent={secondReviewContent}
        isGenerating={isSecondReviewing}
        isCollapsed={isSecondReviewCollapsed}
        onToggleCollapse={onSecondReviewToggle}
        secondReviewRef={secondReviewRef}
        t={t}
      />
    )}
  </div>
  );
};

const RightPanelComponent = React.memo(RightPanelComponentBase, (prevProps, nextProps) => {
  // 自定义比较函数 - 只在关键数据变化时重新渲染
  const isEqual = (
    prevProps.thinking === nextProps.thinking &&
    prevProps.isGenerating === nextProps.isGenerating &&
    prevProps.isThinkingCollapsed === nextProps.isThinkingCollapsed &&
    prevProps.isBrowsing === nextProps.isBrowsing &&
    prevProps.isBrowsingCollapsed === nextProps.isBrowsingCollapsed &&
    prevProps.isReviewing === nextProps.isReviewing &&
    prevProps.isReviewCollapsed === nextProps.isReviewCollapsed &&
    prevProps.isSecondReviewing === nextProps.isSecondReviewing &&
    prevProps.isSecondReviewCollapsed === nextProps.isSecondReviewCollapsed &&
    prevProps.reviewContent === nextProps.reviewContent &&
    prevProps.secondReviewContent === nextProps.secondReviewContent &&
    deepEqual(prevProps.searchResults, nextProps.searchResults) &&
    deepEqual(prevProps.browsingResults, nextProps.browsingResults) &&
    deepEqual(prevProps.workflowNodes, nextProps.workflowNodes) &&
    deepEqual(prevProps.rightPanelNodes, nextProps.rightPanelNodes)
  );

  // 调试：记录比较结果
  console.log('🔍 RightPanelComponent 比较结果:', isEqual, {
    thinking: prevProps.thinking === nextProps.thinking,
    isGenerating: prevProps.isGenerating === nextProps.isGenerating,
    workflowNodesEqual: deepEqual(prevProps.workflowNodes, nextProps.workflowNodes),
    rightPanelNodesEqual: deepEqual(prevProps.rightPanelNodes, nextProps.rightPanelNodes)
  });

  return isEqual;
});

// 移动端布局组件
interface MobileLayoutProps {
  dynamicSteps: Array<{ text: string; completed: boolean; onClick?: () => void; avatar?: React.ReactNode; nodeId?: string }>;
  completedSteps: number;
  totalSteps: number;
  isGenerating: boolean;
  workflowStatus: 'running' | 'finished';
  workflowNodes: WorkflowNode[];
  leftPanelNodes: WorkflowNode[];
  rightPanelNodes: WorkflowNode[];
  currentRunningNodeId?: string;
  onNodeClick?: (nodeId: string) => void;
  t: (key: string, options?: any) => string;
}

const MobileLayout: React.FC<MobileLayoutProps> = ({
  dynamicSteps,
  completedSteps,
  totalSteps,
  isGenerating,
  workflowStatus,
  workflowNodes,
  leftPanelNodes,
  rightPanelNodes,
  currentRunningNodeId,
  onNodeClick,
  t
}) => {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const [isMainCollapsed, setIsMainCollapsed] = useState(false); // 新增：主容器折叠状态
  const mobileContainerRef = useRef<HTMLDivElement>(null);

  // 获取要显示的节点
  const nodesToDisplay = leftPanelNodes && leftPanelNodes.length > 0 ? leftPanelNodes : workflowNodes;

  // 自动滚动到当前执行的节点
  useEffect(() => {
    if (!currentRunningNodeId || !mobileContainerRef.current) return;

    const targetElement = mobileContainerRef.current.querySelector(`[data-mobile-node-id="${currentRunningNodeId}"]`);
    if (targetElement) {
      targetElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  }, [currentRunningNodeId]);

  // 自动展开正在执行的节点，自动折叠已完成的节点
  useEffect(() => {
    // 展开正在执行的节点
    const runningNode = nodesToDisplay.find(node => node.status === 'running');
    if (runningNode && runningNode.outputs?.answer) {
      setExpandedNodes(prev => new Set(prev).add(runningNode.id));
    }

    // 延迟折叠已完成的节点
    const completedNodeIds = nodesToDisplay
      .filter(node => node.status === 'success' && node.outputs?.answer)
      .map(node => node.id);

    if (completedNodeIds.length > 0) {
      const timer = setTimeout(() => {
        setExpandedNodes(current => {
          const updated = new Set(current);
          completedNodeIds.forEach(nodeId => {
            updated.delete(nodeId);
          });
          return updated;
        });
      }, 3000); // 3秒后自动折叠

      return () => clearTimeout(timer);
    }
  }, [nodesToDisplay]);

  const toggleNodeExpansion = (nodeId: string) => {
    setExpandedNodes(prev => {
      const newExpanded = new Set(prev);
      if (newExpanded.has(nodeId)) {
        newExpanded.delete(nodeId);
      } else {
        newExpanded.add(nodeId);
      }
      return newExpanded;
    });
  };

  // 获取父节点状态
  const getParentStatus = () => {
    const hasRunningNodes = nodesToDisplay.some(node => node.status === 'running');
    const allCompleted = nodesToDisplay.every(node => node.status === 'success' || node.status === 'error');

    if (hasRunningNodes || isGenerating) {
      return { text: '进行中', color: 'text-blue-600', bgColor: 'bg-blue-50', borderColor: 'border-blue-200' };
    } else if (allCompleted) {
      return { text: '已完成', color: 'text-green-600', bgColor: 'bg-green-50', borderColor: 'border-green-200' };
    } else {
      return { text: '准备中', color: 'text-gray-600', bgColor: 'bg-gray-50', borderColor: 'border-gray-200' };
    }
  };

  const parentStatus = getParentStatus();

  return (
    <div ref={mobileContainerRef} className="my-4 animate-fade-in">
      {/* 整体统一容器 - 完全模拟参考样式 */}
      <div className="rounded-2xl bg-white border border-gray-200 shadow-sm overflow-hidden transition-all duration-200">

        {/* 顶部标题区域 - 完全按照参考图片 */}
        <div
          className="px-4 py-3 cursor-pointer transition-colors duration-150 hover:bg-gray-50 border-b border-gray-100"
          onClick={() => setIsMainCollapsed(!isMainCollapsed)}
        >
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3">
              <SearchIcon width={16} height={16} className="text-gray-500 mt-0.5" />
              <div className="flex flex-col">
                <div className="flex items-center space-x-1">
                  <span className="font-medium text-gray-900 text-sm">
                    节点加载
                  </span>
                  <span className="text-gray-500 text-sm">
                    • {totalSteps}来源
                  </span>
                </div>
                <span className="text-xs text-gray-500 mt-0.5">
                  {isMainCollapsed ? '展开详情' : '折叠详情'}
                </span>
              </div>
            </div>
            <ChevronDownIcon
              width={16}
              height={16}
              className={`text-gray-400 transition-transform duration-150 ${
                isMainCollapsed ? 'rotate-180' : 'rotate-0'
              }`}
            />
          </div>
        </div>

        {/* 动态生成的工作流节点 - 素雅风格 */}
        <div className={`overflow-hidden transition-all duration-200 ease-out ${
          isMainCollapsed
            ? 'max-h-0 opacity-0'
            : 'max-h-[2000px] opacity-100'
        }`}>
          {nodesToDisplay.map((node, index) => {
            const isExpanded = expandedNodes.has(node.id);
            const isRunning = node.status === 'running';
            const isCompleted = node.status === 'success';
            const hasContent = node.outputs?.answer && node.outputs.answer.trim().length > 0;
            const isLastNode = index === nodesToDisplay.length - 1;

            return (
              <div
                key={node.id}
                data-mobile-node-id={node.id}
                className="px-4 py-2"
              >
              {/* 节点头部 - 完全按照参考图片样式 */}
              <div
                className="cursor-pointer"
                onClick={() => hasContent && toggleNodeExpansion(node.id)}
              >
                <div className="flex items-center space-x-3">
                  {/* 状态图标 - 圆形边框，进行中时转圈 */}
                  <div className={`w-4 h-4 rounded-full flex items-center justify-center relative ${
                    isRunning
                      ? 'border-2 border-blue-500 border-t-transparent animate-spin bg-white'
                      : 'border border-gray-400 bg-white'
                  }`}>
                    {isRunning ? (
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse"></div>
                    ) : isCompleted ? (
                      <CheckIcon
                        width={10}
                        height={10}
                        stroke="currentColor"
                        strokeWidth={2}
                        className="text-gray-800"
                      />
                    ) : null}
                  </div>

                  {/* 节点标题和箭头在一起 */}
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-700 text-sm">
                      {getNodeDisplayName(node.type, node.title) || '未知节点'}
                    </span>

                    {/* 展开/折叠图标 - 只有有内容时才显示 */}
                    {hasContent && (
                      <ChevronDownIcon
                        width={12}
                        height={12}
                        className={`text-gray-400 transition-transform duration-200 ease-in-out ${
                          isExpanded ? 'rotate-180' : 'rotate-0'
                        }`}
                      />
                    )}
                  </div>
                </div>
              </div>

              {/* 节点内容 - 丝滑展开折叠动效 */}
              {hasContent && (
                <div
                  className={`overflow-hidden transition-all duration-300 ease-in-out ${
                    isExpanded
                      ? 'max-h-screen opacity-100 transform translate-y-0'
                      : 'max-h-0 opacity-0 transform -translate-y-2'
                  }`}
                >
                  <div className={`pt-3 pb-2 pl-8 transition-all duration-300 ease-in-out ${
                    isExpanded ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform -translate-y-1'
                  }`}>
                    <div className="text-sm text-gray-700 leading-relaxed">
                      <AiResponseRenderer content={node.outputs.answer} />
                      {isRunning && <ModernCursor color="bg-blue-500" />}
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
        </div>
      </div>
    </div>
  );
};

/**
 * AI助手消息组件 - 显示AI回复的复杂消息结构
 * 包含步骤显示、思考过程、搜索结果、浏览结果和最终回答
 */
const AssistantMessage: React.FC<AssistantMessageProps> = ({
  content,
  thinking = '',
  isGenerating = false,
  searchResults = [],
  browsingResults = [],
  // 新增的状态字段
  isSearching = false,
  isBrowsing = false,
  isThinking = false,
  isGeneratingAnswer = false,
  // 新增：评审相关props
  isReviewing = false,
  reviewContent = '',
  // 新增：复审相关props
  isSecondReviewing = false,
  secondReviewContent = '',
  // 新增：工作流相关props
  workflowNodes = [],
  workflowStatus = 'running',
  // 新增：分类显示的节点数据
  leftPanelNodes = [],
  rightPanelNodes = [],
  resultNodes = []
}) => {
  // 性能监控
  useRenderPerformance('AssistantMessage');

  // 国际化
  const { t } = useSimpleTranslation();

  // 调试：监控主组件渲染次数
  console.log('🔄 AssistantMessage 主组件重新渲染', {
    workflowNodesLength: workflowNodes?.length || 0,
    leftPanelNodesLength: leftPanelNodes?.length || 0,
    rightPanelNodesLength: rightPanelNodes?.length || 0,
    isGenerating,
    content: content?.length || 0
  });
  // 新增：组件展开状态
  const [isExpanded, setIsExpanded] = useState(false)

  // 锚点功能状态
  const [activeNodeId, setActiveNodeId] = useState<string | undefined>(undefined)

  // 自动滚动功能状态
  const [currentRunningNodeId, setCurrentRunningNodeId] = useState<string | undefined>(undefined)
  const leftPanelRef = useRef<HTMLDivElement>(null)
  // 生成带ID的标题组件的通用函数
  const createHeadingComponent = (Tag: keyof JSX.IntrinsicElements) => {
    return ({ children, ...props }: React.ComponentProps<typeof Tag>) => {
      const text = children?.toString() || ''
      const id = `heading-${text.replace(/\s+/g, '-').toLowerCase()}`
      return React.createElement(Tag, { id, ...props }, children)
    }
  }

  // 自定义Markdown组件，为标题添加ID
  const markdownComponents: Components = {
    h1: createHeadingComponent('h1'),
    h2: createHeadingComponent('h2'),
    h3: createHeadingComponent('h3'),
    h4: createHeadingComponent('h4'),
    h5: createHeadingComponent('h5'),
    h6: createHeadingComponent('h6')
  }
  // 思考过程折叠状态
  const [isThinkingCollapsed, setIsThinkingCollapsed] = useState(false)
  // 新增：浏览结果折叠状态
  const [isBrowsingCollapsed, setIsBrowsingCollapsed] = useState(false)
  // 新增：评审过程折叠状态
  const [isReviewCollapsed, setIsReviewCollapsed] = useState(false)
  // 新增：复审过程折叠状态
  const [isSecondReviewCollapsed, setIsSecondReviewCollapsed] = useState(false)
  // 新增：移动端搜索结果折叠状态
  const [isMobileSearchExpanded, setIsMobileSearchExpanded] = useState(false)

  // 右侧内容区域的引用
  const rightPanelRef = useRef<HTMLDivElement>(null)
  const thinkingRef = useRef<HTMLDivElement>(null)
  const searchResultsRef = useRef<HTMLDivElement>(null)
  const browsingResultsRef = useRef<HTMLDivElement>(null)
  const finalAnswerRef = useRef<HTMLDivElement>(null) // 新增：最终回答区域的引用
  // 新增：评审区域的引用
  const reviewRef = useRef<HTMLDivElement>(null)
  // 新增：复审区域的引用
  const secondReviewRef = useRef<HTMLDivElement>(null)
  // 新增：工作流节点数据区域的引用
  const workflowNodeRef = useRef<HTMLDivElement>(null)
  
  // 禁用自动折叠，避免输入时闪烁
  // useEffect(() => {
  //   // 思考过程自动折叠已禁用
  // }, [])
  
  // 禁用自动折叠，避免输入时闪烁
  // useEffect(() => {
  //   // 浏览结果自动折叠已禁用
  // }, [])
  
  // 禁用自动折叠，避免输入时闪烁
  // useEffect(() => {
  //   // 评审过程自动折叠已禁用
  // }, [])
  
  // 禁用自动折叠，避免输入时闪烁
  // useEffect(() => {
  //   // 复审过程自动折叠已禁用
  // }, [])
  
  // 新的右侧面板滚动系统
  const [isUserScrolling, setIsUserScrolling] = useState(false)
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true)
  const scrollTimeoutRef = useRef<number | null>(null)
  const lastContentHeightRef = useRef<number>(0)

  // 检查是否接近底部
  const isNearBottom = useCallback(() => {
    if (!rightPanelRef.current) return false
    const { scrollTop, scrollHeight, clientHeight } = rightPanelRef.current
    return scrollHeight - scrollTop - clientHeight < 100 // 距离底部100px内认为是接近底部
  }, [])

  // 平滑滚动到底部
  const scrollToBottom = useCallback(() => {
    if (!rightPanelRef.current) return
    rightPanelRef.current.scrollTo({
      top: rightPanelRef.current.scrollHeight,
      behavior: 'smooth'
    })
  }, [])

  // 处理滚动事件
  const handleScroll = useCallback(() => {
    if (!rightPanelRef.current) return

    const nearBottom = isNearBottom()
    setShouldAutoScroll(nearBottom)

    // 如果用户手动滚动到非底部区域，标记为用户正在滚动
    if (!nearBottom) {
      setIsUserScrolling(true)
      // 清除之前的定时器
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
      // 用户滚动后，延迟重置滚动状态
      scrollTimeoutRef.current = setTimeout(() => {
        if (isNearBottom()) {
          setIsUserScrolling(false)
        }
      }, 1500) // 1.5秒后重置
    } else {
      setIsUserScrolling(false)
    }

    // 锚点功能：根据右侧面板滚动位置自动高亮左侧面板对应项目
    const rightPanel = rightPanelRef.current
    const scrollTop = rightPanel.scrollTop
    const panelHeight = rightPanel.clientHeight
    const viewportCenter = scrollTop + panelHeight / 2

    // 查找所有带有data-node-id的元素
    const nodeElements = rightPanel.querySelectorAll('[data-node-id]')
    let closestNodeId: string | undefined = undefined
    let closestDistance = Infinity

    nodeElements.forEach((element) => {
      const nodeId = element.getAttribute('data-node-id')
      if (!nodeId) return

      const elementTop = (element as HTMLElement).offsetTop
      const elementHeight = (element as HTMLElement).offsetHeight
      const elementCenter = elementTop + elementHeight / 2

      // 计算元素中心与视口中心的距离
      const distance = Math.abs(elementCenter - viewportCenter)

      // 只考虑在视口内或接近视口的元素
      if (elementTop <= viewportCenter && elementTop + elementHeight >= scrollTop) {
        if (distance < closestDistance) {
          closestDistance = distance
          closestNodeId = nodeId
        }
      }
    })

    // 更新活跃节点ID
    if (closestNodeId && closestNodeId !== activeNodeId) {
      setActiveNodeId(closestNodeId)
    }
  }, [isNearBottom, activeNodeId])

  // 监听内容高度变化，智能滚动
  useEffect(() => {
    if (!rightPanelRef.current) return

    const currentHeight = rightPanelRef.current.scrollHeight
    const heightChanged = currentHeight !== lastContentHeightRef.current

    if (heightChanged) {
      lastContentHeightRef.current = currentHeight

      // 只有在应该自动滚动且用户没有手动滚动时才滚动
      if (shouldAutoScroll && !isUserScrolling) {
        // 添加小延迟，确保DOM更新完成
        setTimeout(() => {
          scrollToBottom()
        }, 100)
      }
    }
  }, [content, thinking, searchResults, browsingResults, reviewContent, secondReviewContent, workflowNodes, leftPanelNodes, rightPanelNodes, resultNodes, shouldAutoScroll, isUserScrolling, scrollToBottom])

  // 绑定滚动事件监听器
  useEffect(() => {
    const rightPanel = rightPanelRef.current
    if (!rightPanel) return

    rightPanel.addEventListener('scroll', handleScroll, { passive: true })

    return () => {
      rightPanel.removeEventListener('scroll', handleScroll)
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
    }
  }, [handleScroll])

  // 当组件首次加载或生成状态改变时，滚动到底部
  useEffect(() => {
    if (isGenerating && shouldAutoScroll) {
      scrollToBottom()
    }
  }, [isGenerating, scrollToBottom, shouldAutoScroll])

  // 左侧面板联动右侧面板滚动功能
  const scrollToTargetSection = useCallback((target: 'thinking' | 'search' | 'browsing' | 'review' | 'secondReview' | 'finalAnswer' | string, nodeId?: string) => {
    if (!rightPanelRef.current) return

    let targetElement: HTMLElement | null = null

    // 根据目标类型找到对应的DOM元素
    if (target === 'thinking' && thinkingRef.current) {
      targetElement = thinkingRef.current
    } else if (target === 'search' && searchResultsRef.current) {
      targetElement = searchResultsRef.current
    } else if (target === 'browsing' && browsingResultsRef.current) {
      targetElement = browsingResultsRef.current
    } else if (target === 'review' && reviewRef.current) {
      targetElement = reviewRef.current
    } else if (target === 'secondReview' && secondReviewRef.current) {
      targetElement = secondReviewRef.current
    } else if (target === 'finalAnswer' && finalAnswerRef.current) {
      // 最终回答使用页面级滚动
      finalAnswerRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
      return
    } else if (typeof target === 'string' && target.startsWith('node-')) {
      // 处理工作流节点
      const extractedNodeId = target.replace('node-', '')
      targetElement = rightPanelRef.current?.querySelector(`[data-node-id="${extractedNodeId}"]`) as HTMLElement
      // 设置活跃节点ID用于高亮左侧面板
      setActiveNodeId(extractedNodeId)
    }

    if (targetElement) {
      // 暂时禁用自动滚动，避免与用户操作冲突
      setIsUserScrolling(true)
      setShouldAutoScroll(false)

      const rightPanel = rightPanelRef.current
      const targetTop = targetElement.offsetTop - rightPanel.offsetTop

      rightPanel.scrollTo({
        top: Math.max(0, targetTop - 20), // 距离顶部20px的偏移
        behavior: 'smooth'
      })

      // 延迟恢复自动滚动状态
      setTimeout(() => {
        setIsUserScrolling(false)
        setShouldAutoScroll(true)
      }, 1000)
    }
  }, [setActiveNodeId])

  // 处理节点点击事件，联动右侧面板滚动
  const handleNodeClick = useCallback((nodeId: string) => {
    scrollToTargetSection(`node-${nodeId}`)
  }, [scrollToTargetSection])

  // 处理左侧面板步骤点击事件
  const handleStepClick = useCallback((nodeId: string) => {
    setActiveNodeId(nodeId)
    scrollToTargetSection(`node-${nodeId}`)
  }, [scrollToTargetSection])

  // 智能滚动系统已移除

  // 工作流节点状态监听 - 检测当前执行节点并触发左侧面板自动滚动
  useEffect(() => {
    if (!workflowNodes || workflowNodes.length === 0) return

    // 查找当前正在执行的节点
    const runningNode = workflowNodes.find(node => node.status === 'running')

    if (runningNode && runningNode.id !== currentRunningNodeId) {
      // 有新的节点开始执行，更新当前执行节点ID
      setCurrentRunningNodeId(runningNode.id)
      console.log('🚀 检测到新的执行节点:', runningNode.id, runningNode.title)
    } else if (!runningNode && currentRunningNodeId) {
      // 没有正在执行的节点了，但之前有执行节点，可能是执行完成
      // 查找最后完成的节点
      const lastCompletedNode = workflowNodes
        .filter(node => node.status === 'success' || node.status === 'error')
        .sort((a, b) => b.created_at - a.created_at)[0]

      if (lastCompletedNode && lastCompletedNode.id !== currentRunningNodeId) {
        setCurrentRunningNodeId(lastCompletedNode.id)
        console.log('✅ 检测到最后完成的节点:', lastCompletedNode.id, lastCompletedNode.title)
      }
    }
  }, [workflowNodes, workflowStatus, currentRunningNodeId])
  // 使用 useMemo 缓存节点数据处理结果
  const processedNodesData = useMemo(() => {
    const nodesToDisplay = leftPanelNodes && leftPanelNodes.length > 0 ? leftPanelNodes : workflowNodes;
    return {
      nodesToDisplay,
      hasNodes: nodesToDisplay && nodesToDisplay.length > 0,
      hasRunningNodes: nodesToDisplay && nodesToDisplay.some(node => node.status === 'running')
    };
  }, [leftPanelNodes, workflowNodes]);

  // 根据工作流节点和传统步骤动态生成步骤显示
  const generateSteps = useCallback(() => {
    const dynamicSteps: { text: string; completed: boolean; onClick?: () => void; avatar?: React.ReactNode; nodeId?: string }[] = []

    const { nodesToDisplay, hasNodes, hasRunningNodes } = processedNodesData;



    // 如果有工作流节点，优先显示工作流步骤
    if (hasNodes) {
      // 处理并行执行的节点
      const parallelGroups = new Map<string, WorkflowNode[]>()
      const sequentialNodes: WorkflowNode[] = []

      // 按时间排序节点
      const sortedNodes = [...nodesToDisplay].sort((a, b) => a.created_at - b.created_at)

      // 分组并行和顺序节点
      sortedNodes.forEach(node => {
        if (node.parallel_id) {
          if (!parallelGroups.has(node.parallel_id)) {
            parallelGroups.set(node.parallel_id, [])
          }
          parallelGroups.get(node.parallel_id)!.push(node)
        } else {
          sequentialNodes.push(node)
        }
      })

      // 生成步骤显示 - 改进的逻辑来正确处理混合的顺序和并行节点
      let processedParallelIds = new Set<string>()

      // 创建一个时间线来正确排序显示步骤
      const timeline: Array<{
        time: number
        type: 'sequential' | 'parallel'
        node?: WorkflowNode
        parallelId?: string
        parallelNodes?: WorkflowNode[]
      }> = []

      sortedNodes.forEach(node => {
        if (node.parallel_id && !processedParallelIds.has(node.parallel_id)) {
          // 并行节点组
          const parallelNodes = parallelGroups.get(node.parallel_id) || []
          timeline.push({
            time: Math.min(...parallelNodes.map(n => n.created_at)),
            type: 'parallel',
            parallelId: node.parallel_id,
            parallelNodes
          })
          processedParallelIds.add(node.parallel_id)
        } else if (!node.parallel_id) {
          // 顺序节点
          timeline.push({
            time: node.created_at,
            type: 'sequential',
            node
          })
        }
      })

      // 按时间排序时间线
      timeline.sort((a, b) => a.time - b.time)

      // 根据时间线生成步骤
      timeline.forEach(item => {
        if (item.type === 'parallel' && item.parallelNodes) {
          // 处理并行节点组 - 显示每个并行节点
          const parallelNodes = item.parallelNodes

          // 按创建时间排序并行节点，确保显示顺序一致
          const sortedParallelNodes = [...parallelNodes].sort((a, b) => a.created_at - b.created_at)

          sortedParallelNodes.forEach(node => {
            const displayName = getNodeDisplayName(node.type, node.title)
            let statusText = ''

            if (node.status === 'error') {
              statusText = `${displayName}${t('chat.executionFailed')}`
            } else if (node.status === 'success') {
              statusText = `${displayName}`
            } else if (node.status === 'running') {
              statusText = `${displayName}`
            } else {
              statusText = `${t('chat.preparing')}${displayName}`
            }



            dynamicSteps.push({
              text: statusText,
              completed: node.status === 'success',
              onClick: () => scrollToTargetSection(`node-${node.id}`),
              nodeId: node.id
            })
          })
        } else if (item.type === 'sequential' && item.node) {
          // 处理顺序节点
          const node = item.node
          const displayName = getNodeDisplayName(node.type, node.title)
          let statusText = ''

          if (node.status === 'error') {
            statusText = `${displayName}${t('chat.executionFailed')}`
          } else if (node.status === 'success') {
            statusText = `${displayName}`
          } else if (node.status === 'running') {
            statusText = `${displayName}`
          } else {
            statusText = `${t('chat.preparing')}${displayName}`
          }



          dynamicSteps.push({
            text: statusText,
            completed: node.status === 'success',
            onClick: () => scrollToTargetSection(`node-${node.id}`),
            nodeId: node.id
          })
        }
      })

      // 智能的"加载中"状态管理
      const shouldShowLoading = (workflowStatus === 'running' || isGenerating) && !hasRunningNodes

      if (shouldShowLoading) {
        dynamicSteps.push({
          text: '正在加载中...',
          completed: false,
          onClick: undefined
        })
      }
      // } else if (content) {
      //   // 有内容但工作流状态不明确的情况
      //   dynamicSteps.push({
      //     text: '已完成回答',
      //     completed: true,
      //     onClick: () => smoothScrollToTarget('finalAnswer')
      //   })
      // }
    }
    return dynamicSteps
  }, [
    processedNodesData,
    workflowStatus,
    isGenerating,
    thinking,
    searchResults,
    browsingResults,
    reviewContent,
    secondReviewContent,
    content,
    isThinking,
    isSearching,
    isBrowsing,
    isReviewing,
    isSecondReviewing,
    isGeneratingAnswer,
    scrollToTargetSection
  ])

  const dynamicSteps = generateSteps()
  const completedSteps = dynamicSteps.filter(step => step.completed).length
  const totalSteps = dynamicSteps.length
  const sourcesCount = searchResults.length + browsingResults.length

  // 执行进度计算（滚动相关逻辑已移除）

  // 使用 useCallback 缓存事件处理函数，避免每次渲染都创建新函数
  const handleExpandToggle = useCallback(() => {
    setIsExpanded(!isExpanded);
  }, [isExpanded]);

  const handleThinkingToggle = useCallback(() => {
    setIsThinkingCollapsed(!isThinkingCollapsed);
  }, [isThinkingCollapsed]);

  const handleBrowsingToggle = useCallback(() => {
    setIsBrowsingCollapsed(!isBrowsingCollapsed);
  }, [isBrowsingCollapsed]);

  const handleReviewToggle = useCallback(() => {
    setIsReviewCollapsed(!isReviewCollapsed);
  }, [isReviewCollapsed]);

  const handleSecondReviewToggle = useCallback(() => {
    setIsSecondReviewCollapsed(!isSecondReviewCollapsed);
  }, [isSecondReviewCollapsed]);

  // 新增：移动端搜索结果切换处理
  const handleMobileSearchToggle = useCallback(() => {
    setIsMobileSearchExpanded(!isMobileSearchExpanded);
  }, [isMobileSearchExpanded]);

  // 使用 useMemo 缓存左侧面板 props，只在依赖变化时重新创建
  const leftPanelProps = useMemo(() => ({
    dynamicSteps,
    completedSteps,
    totalSteps,
    sourcesCount,
    isExpanded,
    isGenerating,
    onExpandToggle: handleExpandToggle,
    activeNodeId,
    onStepClick: handleStepClick,
    currentRunningNodeId,
    leftPanelRef
  }), [dynamicSteps, completedSteps, totalSteps, sourcesCount, isExpanded, isGenerating, handleExpandToggle, activeNodeId, handleStepClick, currentRunningNodeId]);

  // 使用 useMemo 缓存右侧面板 props，只在依赖变化时重新创建
  const rightPanelProps = useMemo(() => ({
    thinking,
    isGenerating,
    isThinkingCollapsed,
    onThinkingToggle: handleThinkingToggle,
    thinkingRef,
    searchResults,
    searchResultsRef,
    browsingResults,
    isBrowsing,
    isBrowsingCollapsed,
    onBrowsingToggle: handleBrowsingToggle,
    browsingResultsRef,
    workflowNodes,
    rightPanelNodes,
    workflowNodeRef,
    onNodeClick: handleNodeClick,
    reviewContent,
    isReviewing,
    isReviewCollapsed,
    onReviewToggle: handleReviewToggle,
    reviewRef,
    secondReviewContent,
    isSecondReviewing,
    isSecondReviewCollapsed,
    onSecondReviewToggle: handleSecondReviewToggle,
    secondReviewRef,
    rightPanelRef,
    t
  }), [
    thinking, isGenerating, isThinkingCollapsed, handleThinkingToggle, thinkingRef,
    searchResults, searchResultsRef, browsingResults, isBrowsing, isBrowsingCollapsed,
    handleBrowsingToggle, browsingResultsRef, workflowNodes, rightPanelNodes,
    workflowNodeRef, handleNodeClick, reviewContent, isReviewing, isReviewCollapsed,
    handleReviewToggle, reviewRef, secondReviewContent, isSecondReviewing,
    isSecondReviewCollapsed, handleSecondReviewToggle, secondReviewRef, rightPanelRef, t
  ]);



  // 最终回答组件 - 只显示 result 类型节点的内容
  const FinalAnswer: React.FC = useMemo(() => React.memo(() => {
    // 只显示 result 类型的节点内容，不回退到传统 content
    const resultContent = resultNodes
      ?.filter(node => node.outputs?.answer && node.outputs.answer.trim().length > 0)
      ?.map(node => node.outputs.answer)
      ?.join('\n\n') || content

    // 如果没有 result 节点内容，不显示任何内容
    if (!resultContent) {
      return null
    }

    return (
      <div ref={finalAnswerRef} className="mr-auto max-w-4xl mt-4 mx-auto"> {/* 新增：添加ref引用 */}
        <div className="text-base text-gray-800 leading-relaxed prose prose-base max-w-none">
          <AiResponseRenderer key="ai-response" content={resultContent} fontSize="lg" />
          {/* 检查是否有 result 节点正在运行 */}
          {resultNodes?.some(node => node.status === 'running') && <ModernCursor color="bg-gray-700" />}
        </div>
      </div>
    )
  }), [resultNodes, content])

  return (
    <>
    {/* 移动端布局 */}
    {(leftPanelNodes.length > 0 || workflowNodes.length > 0) && (
      <div className="block md:hidden">
        <MobileLayout
          dynamicSteps={dynamicSteps}
          completedSteps={completedSteps}
          totalSteps={totalSteps}
          isGenerating={isGenerating}
          workflowStatus={workflowStatus}
          workflowNodes={workflowNodes}
          leftPanelNodes={leftPanelNodes}
          rightPanelNodes={rightPanelNodes}
          currentRunningNodeId={currentRunningNodeId}
          onNodeClick={handleNodeClick}
          t={t}
        />
      </div>
    )}
      {/* 桌面端双面板布局 */}
      {(leftPanelNodes.length>0 || workflowNodes.length>0)&&(<div className={`mx-auto relative group ${
        isExpanded ? 'w-6xl max-w-6xl' : 'w-6xl max-w-4xl'
      } px-4 sm:px-0`}>
        <div className={`bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300 ${
          isExpanded ? 'h-[552px]' : 'h-[442px]'
        } hidden md:block`}>
          <div className="flex h-full">
            <LeftPanelComponent {...leftPanelProps} />
            <RightPanelComponent {...rightPanelProps} />
          </div>
        </div>
      </div>)}
      <FinalAnswer />
    </>
  )
}

export default React.memo(AssistantMessage, (prevProps, nextProps) => {
  // 自定义比较函数，只在关键内容发生变化时才重新渲染
  const keysToCompare: (keyof AssistantMessageProps)[] = [
    'content', 'thinking', 'isGenerating',
    'reviewContent', 'secondReviewContent',
    'isReviewing', 'isSecondReviewing',
    'workflowStatus'
  ];

  // 比较基本属性
  for (const key of keysToCompare) {
    if (prevProps[key] !== nextProps[key]) {
      return false;
    }
  }

  // 深度比较数组属性
  const arrayKeysToCompare: (keyof AssistantMessageProps)[] = [
    'searchResults', 'browsingResults', 'workflowNodes',
    'leftPanelNodes', 'rightPanelNodes', 'resultNodes'
  ];

  for (const key of arrayKeysToCompare) {
    if (!deepEqual(prevProps[key], nextProps[key])) {
      return false;
    }
  }

  return true;
});