import React from 'react'
import { useSimpleTranslation, useI18nRouter } from '../i18n/simple-hooks'
import { LANGUAGE_CONFIG, SUPPORTED_LANGUAGES } from '../i18n/simple'

const SimpleI18nTest: React.FC = () => {
  const { t, currentLanguage } = useSimpleTranslation()
  const { changeLanguage, pathname } = useI18nRouter()

  // 添加调试信息
  console.log('[SimpleI18nTest] Rendering with language:', currentLanguage, 'pathname:', pathname)

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">国际化功能测试</h1>

      <div className="mb-6 p-4 bg-gray-100 rounded">
        <h2 className="text-lg font-semibold mb-2">当前状态</h2>
        <p><strong>当前语言:</strong> {currentLanguage}</p>
        <p><strong>当前路径:</strong> {pathname}</p>
        <p><strong>URL语言标识:</strong> {pathname.split('/')[1] || '无'}</p>
      </div>

      <div className="mb-6 p-4 bg-blue-50 rounded">
        <h2 className="text-lg font-semibold mb-2">翻译测试</h2>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p><strong>标题:</strong> {t('header.title')}</p>
            <p><strong>加载中:</strong> {t('common.loading')}</p>
            <p><strong>成功:</strong> {t('common.success')}</p>
            <p><strong>错误:</strong> {t('common.error')}</p>
          </div>
          <div>
            <p><strong>登录:</strong> {t('auth.loginButton')}</p>
            <p><strong>退出:</strong> {t('auth.logoutButton')}</p>
            <p><strong>确认:</strong> {t('common.confirm')}</p>
            <p><strong>取消:</strong> {t('common.cancel')}</p>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-2">语言切换测试</h2>
        <p className="text-sm text-gray-600 mb-3">
          点击下面的按钮应该同时更新URL和页面文本
        </p>
        <div className="space-x-2">
          {SUPPORTED_LANGUAGES.map(lang => (
            <button
              key={lang}
              onClick={() => changeLanguage(lang)}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                currentLanguage === lang
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {LANGUAGE_CONFIG[lang].flag} {LANGUAGE_CONFIG[lang].name}
            </button>
          ))}
        </div>
      </div>

      <div className="p-4 bg-green-50 rounded">
        <h2 className="text-lg font-semibold mb-2">预期行为</h2>
        <ul className="text-sm space-y-1">
          <li>✅ 点击语言按钮后，URL应该立即更新（如：/zh/novax-base → /en/novax-base）</li>
          <li>✅ 页面上的所有文本应该立即切换到对应语言</li>
          <li>✅ 当前语言状态应该正确显示</li>
          <li>✅ 刷新页面后语言设置应该保持</li>
        </ul>
      </div>
    </div>
  )
}

export default SimpleI18nTest
