@tailwind base;
@tailwind components;
@tailwind utilities;

/* HR标签样式 - 上下边距缩小三分之二 */
hr {
  margin-top: 1rem !important;    /* 从默认1rem缩小三分之二到0.33rem */
  margin-bottom: 1rem !important; /* 从默认1rem缩小三分之二到0.33rem */
}

/* PC端HR标签样式 - 进一步缩小边距 */
@media (min-width: 768px) {
  hr {
    margin-top: 1rem !important ;    /* PC端进一步缩小三分之二 */
    margin-bottom: 1rem !important; /* PC端进一步缩小三分之二 */
  }
}

/* AI响应渲染器中的HR样式 - 最高权重 */
.ai-response-container .ai-response-hr {
  margin-top: 0.33rem !important;
  margin-bottom: 0.33rem !important;
  border: none !important;
  border-top: 1px solid #e5e7eb !important;
}

/* 更高权重的选择器 */
.ai-response-container hr.ai-response-hr {
  margin-top: 0.33rem !important;
  margin-bottom: 0.33rem !important;
}

/* 最高权重的选择器 */
div.ai-response-container hr.ai-response-hr {
  margin-top: 0.33rem !important;
  margin-bottom: 0.33rem !important;
}

/* 自定义CSS变量 */
:root {
  --primary-blue: #4D5EFF;
  --light-blue: #ECF2FF;
  --blue-border: #DDE8FF;
  --text-primary: #333B46;
  --text-secondary: #979FAB;
  --bg-sidebar: #F0F2F5;
  --bg-main: #F8F7F6;

  /* 导航栏间距变量 */
  --header-nav-gap-mobile: 1rem;    /* 16px */
  --header-nav-gap-desktop: 1.5rem; /* 24px */
}

/* 全局样式 */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  color: var(--text-primary);
  background-color: var(--bg-main);
}

/* 滚动性能优化 */
.scroll-smooth {
  scroll-behavior: smooth;
}

/* 隐藏滚动条但保持滚动功能 */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

.transform-gpu {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 防止滚动时的闪烁 */
.left-panel-content,
.right-panel-content {
  -webkit-overflow-scrolling: touch;
  contain: layout style paint;
  will-change: scroll-position;
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* 减少重绘和回流 */
.step-item {
  contain: layout style;
  transform: translateZ(0);
}

/* 优化滚动性能 */
.scroll-smooth {
  scroll-behavior: smooth;
  -webkit-scroll-behavior: smooth;
}

/* 优化滚动条性能 */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.scrollbar-thin:hover::-webkit-scrollbar-thumb {
  background-color: rgba(107, 114, 128, 0.7);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 呼吸灯动画效果 */
@keyframes breathingLight {
  0% {
    opacity: 0.3;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
  }
  100% {
    opacity: 0.3;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
}

.animate-slow-pulse {
  animation: breathingLight 2.5s ease-in-out infinite;
}

/* 现代光标动画效果 */
@keyframes modernCursor {
  0%, 50% {
    opacity: 1;
    transform: scaleY(1);
  }
  51%, 100% {
    opacity: 0.3;
    transform: scaleY(0.8);
  }
}

.animate-cursor {
  animation: modernCursor 1.2s ease-in-out infinite;
}

/* 彩色光标动画 - 根据不同颜色主题 */
@keyframes colorfulCursor {
  0%, 50% {
    opacity: 1;
    transform: scaleY(1);
    filter: brightness(1);
  }
  51%, 100% {
    opacity: 0.4;
    transform: scaleY(0.9);
    filter: brightness(0.8);
  }
}

/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

/* 场景选择按钮内联元素过渡 */
.scene-select-button span {
  transition: all 0.3s ease-in-out;
}

/* 自定义组件样式 */
.agent-input {
  background: var(--bg-main);
  border: 1px solid var(--blue-border);
  border-radius: 16px;
}

.agent-input:focus {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(77, 94, 255, 0.1);
}

.agent-mode-active {
  background: var(--light-blue);
  color: var(--primary-blue);
  border-color: var(--blue-border);
}

/* 美化按钮样式 */
.modern-button {
  border-radius: 12px;
  transition: all 0.2s ease-in-out;
  border: none;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modern-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.modern-button:active {
  transform: translateY(0);
}

/* 智能体模式卡片 */
.agent-card {
  border-radius: 16px;
  padding: 12px 8px;
  border: 2px solid transparent;
  transition: all 0.2s ease-in-out;
  background: white;
  position: relative;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

@media (min-width: 768px) {
  .agent-card {
    padding: 16px;
    min-height: 100px;
  }
}

.agent-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.agent-card.active {
  border-color: var(--primary-blue);
  background: var(--light-blue);
  box-shadow: 0 4px 20px rgba(77, 94, 255, 0.15);
}

.agent-icon {
  font-size: 20px;
  margin-bottom: 6px;
  line-height: 1;
}

@media (min-width: 768px) {
  .agent-icon {
    font-size: 24px;
    margin-bottom: 8px;
  }
}

/* 树形菜单连接线样式 */
.tree-item[data-v-b003324d] {
  position: relative; /* 为伪元素提供定位上下文 */
  padding-left: 10px; /* 为连接线留出空间 */
  line-height: 1.5; /* 设置固定行高 */
  padding-top: 4px; /* 上下内边距 */
  padding-bottom: 4px;
  height: 32px; /* 设置固定高度 */
  width: 100%; /* 设置宽度为100% */
  overflow: visible; /* 允许伪元素显示 */
  display: block; /* 改为block布局以确保省略号生效 */
  /* 使用line-height和padding实现垂直居中 */
}

/* 文字内容容器，处理溢出省略号 */
.tree-item[data-v-b003324d] .tree-item-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  display: block;
}

/* 水平连接线 - 所有元素都有横向连接线 */
.tree-item[data-v-b003324d]:before {
  content: "";
  position: absolute;
  top: 50%; /* 垂直居中 */
  left: -12px; /* 调整左边距 */
  width: 12px; /* 水平线长度 */
  height: 1px;
  border-bottom: 1px solid #d0d5e0; /* 调淡颜色 */
}

/* 倒数第二个元素的水平连接线 - 添加弧形 */
.tree-item[data-v-b003324d]:nth-last-child(2):before {
  top: calc(100% - 21px);
  height: 12px; /* 设置高度以创建弧形 */
  border-left: 1px solid  #d0d5e0; /* 调淡颜色 */
  border-bottom-left-radius: 6px; /* 只有最后一个元素有圆角 */
  transform: translateY(-50%); /* 精确居中 */
}

/* 垂直连接线 - 除了第一个元素 */
.tree-item[data-v-b003324d]:not(:first-child):after {
  content: "";
  position: absolute;
  top: calc(-100% - 2px);
  left: -12px;
  width: 1px;
  height: calc(100% + 15px);
  border-left: 1px solid  #d0d5e0; /* 调淡颜色 */
}



/* 渐变按钮 */
.gradient-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  border-radius: 10px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease-in-out;
  display: flex;
  align-items: center;
  gap: 6px;
}

.gradient-button.invite {
  background: linear-gradient(135deg, rgba(255, 154, 158, 0.2) 0%, rgba(255, 107, 107, 0.15) 100%);
  color: #ff6b6b;
  border: 1px solid rgba(255, 107, 107, 0.3);
  backdrop-filter: blur(10px);
}

.gradient-button.points {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(243, 156, 18, 0.15) 100%);
  color: #f39c12;
  border: 1px solid rgba(243, 156, 18, 0.3);
  backdrop-filter: blur(10px);
}

/* 修复订阅按钮的focus和active状态 */
.gradient-button:focus {
  outline: none;
  border: 1px solid rgba(243, 156, 18, 0.3);
}

.gradient-button.points:focus {
  border: 1px solid rgba(243, 156, 18, 0.3);
  box-shadow: 0 0 0 2px rgba(243, 156, 18, 0.2);
}

.gradient-button.points:focus-visible {
  border: 1px solid rgba(243, 156, 18, 0.3);
  outline: none;
}

.gradient-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.gradient-button:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* 版本切换按钮 */
.version-switcher {
  background: rgba(139, 69, 19, 0.08);
  border: 1px solid rgba(139, 69, 19, 0.15);
  color: #8b4513;
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.version-switcher:hover {
  background: rgba(139, 69, 19, 0.12);
  transform: translateY(-1px);
}

/* 用户案例卡片 */
.case-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.case-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

.case-card-image {
  width: 100%;
  height: 120px;
  background: #f8fafc;
  border-radius: 8px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e2e8f0;
}

/* 用户菜单弹出框 - 优化设计 */
.user-menu {
  position: absolute;
  top: 100%;
  right: 0;
  mt: 2;
  background: white;
  border-radius: 16px;
  padding: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  min-width: 180px;
  z-index: 50;
  backdrop-filter: blur(8px);
}

.user-menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 12px;
  transition: all 0.2s ease-in-out;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  font-weight: 500;
}

.user-menu-item:hover {
  background: #f8fafc;
  transform: translateX(2px);
}

.user-menu-item.danger {
  color: #ef4444;
}

.user-menu-item.danger:hover {
  background: #fef2f2;
  color: #dc2626;
}

/* 左侧导航美化 */
.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 12px;
  transition: all 0.2s ease-in-out;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  margin-bottom: 4px;
}

.nav-item:hover {
  background: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
}

.nav-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.nav-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 场景选择弹出框 - 桌面端 */
.scene-popup {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 8px;
  background: white;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  z-index: 30;
  max-width: 600px;
  width: 100vw;
}

@media (min-width: 768px) {
  .scene-popup {
    width: 600px;
  }
}

/* 移动端场景弹出框 */
.mobile-scene-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  z-index: 50;
  padding: 16px;
  overflow-y: auto;
}

.mobile-scene-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.scene-category {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  border-radius: 12px;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  text-align: center;
  border: 1px solid transparent;
}

.scene-category:hover {
  background: #f8fafc;
  border-color: #e2e8f0;
  transform: translateY(-2px);
}

.mobile-scene-category {
  padding: 16px 12px;
  min-height: 120px;
}

.scene-icon {
  width: 40px;
  height: 40px;
  background: transparent;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  font-size: 18px;
}

.mobile-scene-category .scene-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 8px;
  font-size: 18px;
}

.scene-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
  font-size: 14px;
}

.mobile-scene-category .scene-title {
  font-size: 18px;
  margin-bottom: 4px;
  font-weight: 600;
}

.scene-desc {
  color: #6b7280;
  font-size: 12px;
  line-height: 1.4;
}

.mobile-scene-category .scene-desc {
  font-size: 16px;
  line-height: 1.5;
  color: #6b7280;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  /* 防止移动端点击时出现高亮 */
  button, a {
    -webkit-tap-highlight-color: transparent;
  }

  .gradient-button {
    padding: 4px 8px;
    font-size: 11px;
    gap: 2px;
  }

  .case-card-image {
    height: 160px;
  }

  .case-card {
    padding: 12px;
  }

  .mobile-scene-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .mobile-scene-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 更好的焦点样式 */
.focus-visible {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* 订阅按钮特殊处理 - 防止失去焦点后保持黑色边框 */
.gradient-button.points:not(:focus):not(:hover):not(:active) {
  border: 1px solid rgba(243, 156, 18, 0.3) !important;
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 禁用选择高亮 - 在某些交互元素上 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Markdown 代码块样式优化 */
.prose code,.prose pre,.prose pre code  {
  font-size: .95em !important; /* 增大内联代码字体 */

}

/* 语言切换器样式 */
.lang-switcher-button {
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
  border-radius: 0.5rem; /* 8px */
  padding: 0.5rem; /* 8px */
}

.lang-switcher-button:hover {
  background-color: var(--hover-bg-color, #f3f4f6); /* 默认灰色，可由主题覆盖 */
}

.lang-switcher-button .icon-global,
.lang-switcher-button .icon-chevron {
  transition: transform 0.2s ease-in-out;
}

.lang-switcher-button span {
  transition: opacity 0.3s ease-in-out;
}

/* 语言下拉菜单样式 */
.lang-menu {
  background-color: var(--bg-main, #ffffff);
  border-radius: 0.375rem; /* 6px */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border-color, #e5e7eb);
}

.lang-menu a {
  transition: background-color 0.15s ease-in-out, color 0.15s ease-in-out;
  padding: 0.5rem 1rem; /* 8px 16px */
}

.lang-menu a:hover {
  background-color: var(--hover-bg-color-light, #f9fafb);
  color: var(--text-color-hover, #1f2937);
}

.lang-menu a.font-semibold {
  background-color: var(--primary-color-light, #eff6ff); /* 选中项背景 */
  color: var(--primary-color, #2563eb); /* 选中项文字颜色 */
}

/* 确保 fade-in 动画存在 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.2s ease-out forwards;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 简化的停止按钮动画效果 */
@keyframes simpleRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 发送按钮起飞动画 */
@keyframes takeoff {
  0% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }
  30% {
    transform: translateX(2px) translateY(-2px) rotate(5deg);
  }
  60% {
    transform: translateX(-1px) translateY(-1px) rotate(-2deg);
  }
  100% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }
}

/* 保留基本的发送按钮动画 */

/* 移动端优化 */
@media (max-width: 768px) {
  /* 防止移动端点击时出现高亮 */
  button, a {
    -webkit-tap-highlight-color: transparent;
  }

  .gradient-button {
    padding: 4px 8px;
    font-size: 11px;
    gap: 2px;
  }

  .case-card-image {
    height: 160px;
  }

  .case-card {
    padding: 12px;
  }

  .mobile-scene-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .mobile-scene-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 更好的焦点样式 */
.focus-visible {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 禁用选择高亮 - 在某些交互元素上 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 导航栏间距统一样式 - 使用Tailwind space-x 替代 */
/* 保留CSS变量供其他地方使用 */

/* Markdown 代码块样式优化 */
.prose code,.prose pre,.prose pre code  {
  font-size: .95em !important; /* 增大内联代码字体 */

}

/* Emoji 支持样式 */
.emoji-support {
  /* 确保 emoji 正确显示 */
  font-family: 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', 'Segoe UI Symbol', 'Android Emoji', 'EmojiSymbols', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;

  /* 优化 emoji 渲染 */
  font-variant-emoji: emoji;
  text-rendering: optimizeLegibility;

  /* 确保 emoji 不会被拉伸 */
  font-feature-settings: "liga" 1, "kern" 1;

  /* 改善 emoji 在不同系统上的显示 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 针对 AI 响应容器的 emoji 优化 */
.ai-response-container .emoji-support {
  /* 确保 emoji 与文本对齐 */
  vertical-align: baseline;

  /* 保持合适的行高 */
  line-height: inherit;
}

/* 针对不同大小的 emoji 显示优化 */
.ai-response-container.text-lg .emoji-support {
  font-size: 1.125rem; /* 18px */
}

.ai-response-container.text-base .emoji-support {
  font-size: 1rem; /* 16px */
}

.ai-response-container.text-sm .emoji-support {
  font-size: 0.875rem; /* 14px */
}

.selected-conversation {
  background-color: #e5e7eb; /* 灰色背景 */
  color: #1f2937; /* 深灰色文字 */
  font-weight: 400;
}

/* H5订阅弹框动画 */
@keyframes slideUpFromBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDownToBottom {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
}

.h5-modal-enter {
  animation: slideUpFromBottom 0.3s ease-out forwards;
}

.h5-modal-exit {
  animation: slideDownToBottom 0.3s ease-in forwards;
}

/* H5订阅弹框特定样式 */
.h5-subscription-modal {
  /* 确保在移动端有足够的安全区域 */
  padding-bottom: env(safe-area-inset-bottom);
}

/* 垂直滚动动画 - 真正无缝循环 */
@keyframes scrollVertical {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
  }
}

.animate-scroll-vertical {
  animation: scrollVertical 30s linear infinite;
  /* 确保内容高度是容器的两倍 */
  min-height: 200%;
}

/* 移动端触摸优化 */
@media (max-width: 768px) {
  .h5-subscription-modal .qr-code {
    /* 确保二维码在移动端清晰显示 */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  .h5-subscription-modal button {
    /* 增加移动端按钮的触摸区域 */
    min-height: 44px;
  }

  /* 套餐横向滑动优化 */
  .h5-subscription-modal .overflow-x-auto {
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .h5-subscription-modal .overflow-x-auto::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }

  /* 平滑滚动 */
  .h5-subscription-modal .overflow-x-auto {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }
}


