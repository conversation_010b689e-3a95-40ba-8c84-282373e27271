import React, { useState } from 'react'
import AssistantMessage from './chat/AssistantMessage'

// 模拟工作流节点数据
const createMockNode = (id: string, title: string, status: 'running' | 'success' | 'error' = 'running') => ({
  id,
  type: 'llm',
  title,
  status,
  created_at: Date.now(),
  displayCategory: title.toUpperCase().includes('SHOW') ? 'left' as const :
                   title.toUpperCase().includes('DETAIL') ? 'right' as const :
                   title.toUpperCase().includes('RESULT') ? 'result' as const : 'none' as const,
  outputs: status === 'success' ? { answer: `${title} 的输出内容` } : undefined
})

const NodeClassificationTest: React.FC = () => {
  const [nodes, setNodes] = useState([
    createMockNode('node-1', 'SHOW-用户需求分析', 'running'),
    createMockNode('node-2', 'DETAIL-详细处理过程', 'running'),
    createMockNode('node-3', 'RESULT-最终结果输出', 'running'),
    createMockNode('node-4', '内部处理节点', 'running')
  ])

  // 分类节点数据
  const leftPanelNodes = nodes.filter(n => n.displayCategory === 'left')
  const rightPanelNodes = nodes.filter(n => n.displayCategory === 'right')
  const resultNodes = nodes.filter(n => n.displayCategory === 'result')

  const handleCompleteNode = (nodeId: string) => {
    setNodes(prev => prev.map(node => 
      node.id === nodeId 
        ? { ...node, status: 'success' as const, outputs: { answer: `${node.title} 已完成的输出内容` } }
        : node
    ))
  }

  const handleCompleteAll = () => {
    setNodes(prev => prev.map(node => ({
      ...node,
      status: 'success' as const,
      outputs: { answer: `${node.title} 已完成的输出内容` }
    })))
  }

  const handleResetAll = () => {
    setNodes(prev => prev.map(node => ({
      ...node,
      status: 'running' as const,
      outputs: undefined
    })))
  }

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">节点分类显示测试</h1>
        
        {/* 控制按钮 */}
        <div className="mb-6 space-x-4">
          <button
            onClick={handleCompleteAll}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            完成所有节点
          </button>
          <button
            onClick={handleResetAll}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            重置所有节点
          </button>
          {nodes.map(node => (
            <button
              key={node.id}
              onClick={() => handleCompleteNode(node.id)}
              className={`px-3 py-1 text-sm rounded ${
                node.status === 'success' 
                  ? 'bg-green-200 text-green-800' 
                  : 'bg-blue-500 text-white hover:bg-blue-600'
              }`}
            >
              完成 {node.title.split('-')[1] || node.title}
            </button>
          ))}
        </div>

        {/* 状态显示 */}
        <div className="mb-6 grid grid-cols-4 gap-4 text-sm">
          <div className="bg-white p-3 rounded shadow">
            <h3 className="font-medium mb-2">左侧面板节点</h3>
            <p>数量: {leftPanelNodes.length}</p>
            {leftPanelNodes.map(n => (
              <div key={n.id} className={`text-xs ${n.status === 'success' ? 'text-green-600' : 'text-blue-600'}`}>
                {n.title} - {n.status}
              </div>
            ))}
          </div>
          <div className="bg-white p-3 rounded shadow">
            <h3 className="font-medium mb-2">右侧面板节点</h3>
            <p>数量: {rightPanelNodes.length}</p>
            {rightPanelNodes.map(n => (
              <div key={n.id} className={`text-xs ${n.status === 'success' ? 'text-green-600' : 'text-blue-600'}`}>
                {n.title} - {n.status}
              </div>
            ))}
          </div>
          <div className="bg-white p-3 rounded shadow">
            <h3 className="font-medium mb-2">结果组件节点</h3>
            <p>数量: {resultNodes.length}</p>
            {resultNodes.map(n => (
              <div key={n.id} className={`text-xs ${n.status === 'success' ? 'text-green-600' : 'text-blue-600'}`}>
                {n.title} - {n.status}
              </div>
            ))}
          </div>
          <div className="bg-white p-3 rounded shadow">
            <h3 className="font-medium mb-2">所有节点</h3>
            <p>数量: {nodes.length}</p>
            <p>已完成: {nodes.filter(n => n.status === 'success').length}</p>
          </div>
        </div>

        {/* AssistantMessage 组件测试 */}
        <div className="bg-white rounded-lg shadow-lg">
          <AssistantMessage
            content="这是测试内容，用于验证节点分类显示功能。"
            leftPanelNodes={leftPanelNodes}
            rightPanelNodes={rightPanelNodes}
            resultNodes={resultNodes}
            workflowNodes={nodes}
            workflowStatus="running"
            isGenerating={false}
          />
        </div>
      </div>
    </div>
  )
}

export default NodeClassificationTest
